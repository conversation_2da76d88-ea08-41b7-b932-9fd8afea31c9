#!/usr/bin/env python3
"""
Demo script to show delete and clear buttons functionality
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_delete_button():
    """Demo the delete button functionality"""
    try:
        from gui.main_window import EnhancedMainWindow
        
        # Create a minimal window to test the delete dialog
        root = tk.Tk()
        root.title("🗑️ Delete Button Demo")
        root.geometry("400x300")
        root.configure(bg='#E8F4FD')
        
        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"400x300+{x}+{y}")
        
        # Create main window instance for database access
        main_window = EnhancedMainWindow()
        main_window.root.withdraw()  # Hide the main window
        
        # Header
        header_frame = tk.Frame(root, bg='#2E86AB', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame,
                              text="🗑️ Delete & Clear Buttons Demo",
                              font=('Arial', 16, 'bold'),
                              bg='#2E86AB',
                              fg='white')
        header_label.pack(pady=15)
        
        # Content
        content_frame = tk.Frame(root, bg='#E8F4FD')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Info
        info_label = tk.Label(content_frame,
                            text="Click the buttons below to test the delete and clear functionality.\n\n"
                                 "The dialogs will show real data from your database:\n"
                                 "• 13,222+ total detection records\n"
                                 "• Preview before deletion\n"
                                 "• Confidence threshold filtering\n"
                                 "• Time range filtering",
                            font=('Arial', 11),
                            bg='#E8F4FD',
                            fg='#2C3E50',
                            justify='center')
        info_label.pack(pady=20)
        
        # Buttons
        button_frame = tk.Frame(content_frame, bg='#E8F4FD')
        button_frame.pack(pady=20)
        
        # Delete Data button
        delete_btn = tk.Button(button_frame,
                             text="🗑️ Test Delete Data",
                             font=('Arial', 12, 'bold'),
                             bg='#E74C3C',
                             fg='white',
                             relief='flat',
                             bd=0,
                             pady=12,
                             padx=20,
                             cursor='hand2',
                             command=main_window.show_delete_data_dialog)
        delete_btn.pack(pady=10, fill='x')
        
        # Clear Old Data button
        clear_btn = tk.Button(button_frame,
                            text="🧹 Test Clear Old Data",
                            font=('Arial', 12, 'bold'),
                            bg='#F39C12',
                            fg='white',
                            relief='flat',
                            bd=0,
                            pady=12,
                            padx=20,
                            cursor='hand2',
                            command=main_window.show_clear_old_data_dialog)
        clear_btn.pack(pady=10, fill='x')
        
        # Close button
        close_btn = tk.Button(button_frame,
                            text="❌ Close Demo",
                            font=('Arial', 11, 'bold'),
                            bg='#95A5A6',
                            fg='white',
                            relief='flat',
                            bd=0,
                            pady=10,
                            padx=20,
                            cursor='hand2',
                            command=lambda: [main_window.root.destroy(), root.destroy()])
        close_btn.pack(pady=20, fill='x')
        
        # Status
        status_label = tk.Label(content_frame,
                              text="✅ Database connected with real data ready for testing",
                              font=('Arial', 10),
                              bg='#E8F4FD',
                              fg='#27AE60')
        status_label.pack(side='bottom')
        
        print("🎯 Delete buttons demo started!")
        print("💡 Click the buttons to test the delete and clear functionality")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error in demo: {e}")
        messagebox.showerror("Demo Error", f"Failed to start demo: {e}")

def main():
    """Main demo function"""
    print("🎯 STARTING DELETE BUTTONS DEMO")
    print("=" * 40)
    print("This demo will show the delete and clear buttons in action.")
    print("You can test them with real database data safely.")
    print()
    
    try:
        demo_delete_button()
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo error: {e}")

if __name__ == "__main__":
    main()
