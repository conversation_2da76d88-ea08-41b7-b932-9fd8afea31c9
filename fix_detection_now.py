#!/usr/bin/env python3
"""
EMERGENCY FIX: Diagnose and fix facial expression detection issues immediately
"""

import cv2
import numpy as np
import time
import os
import sys
import traceback

def test_basic_detection():
    """Test basic detection functionality"""
    print("🚨 EMERGENCY DETECTION TEST")
    print("=" * 50)
    
    try:
        # Import the detection system
        sys.path.append('detection')
        from custom_yolo_expression import CustomYOLOv8ExpressionDetector
        
        print("✅ Successfully imported detection system")
        
        # Create detector instance
        print("🔄 Creating detector instance...")
        detector = CustomYOLOv8ExpressionDetector()
        
        # Check if model is loaded
        if detector.yolo_model is None:
            print("❌ CRITICAL: YOLOv8 model not loaded!")
            print("📁 Checking model file...")
            
            model_path = "models/emotion_detection_83.6_percent.pt"
            if os.path.exists(model_path):
                print(f"✅ Model file exists: {model_path}")
                print(f"📊 File size: {os.path.getsize(model_path):,} bytes")
            else:
                print(f"❌ Model file missing: {model_path}")
                return False
        else:
            print("✅ YOLOv8 model loaded successfully")
        
        # Test with a simple frame
        print("🧪 Testing with dummy frame...")
        test_frame = np.random.randint(50, 200, (480, 640, 3), dtype=np.uint8)
        
        # Add a simple face-like pattern
        cv2.rectangle(test_frame, (250, 150), (390, 330), (150, 150, 150), -1)  # Face
        cv2.circle(test_frame, (290, 200), 10, (50, 50, 50), -1)  # Left eye
        cv2.circle(test_frame, (350, 200), 10, (50, 50, 50), -1)  # Right eye
        cv2.rectangle(test_frame, (310, 250), (330, 270), (50, 50, 50), -1)  # Nose
        cv2.ellipse(test_frame, (320, 290), (20, 10), 0, 0, 180, (50, 50, 50), 2)  # Mouth
        
        print("🎭 Running detection on test frame...")
        start_time = time.time()
        
        try:
            success = detector.capture_and_detect(test_frame)
            detection_time = time.time() - start_time
            
            if success:
                print(f"✅ Detection successful in {detection_time:.3f}s")
                
                # Get results
                result = detector.get_last_detection()
                if result:
                    print(f"🎯 Detected: {result.get('expression', 'Unknown')}")
                    print(f"📊 Confidence: {result.get('confidence', 0.0):.3f}")
                    print(f"🤖 Model: {result.get('model_used', 'Unknown')}")
                else:
                    print("⚠️ No detection result available")
                
                return True
            else:
                print("❌ Detection failed")
                return False
                
        except Exception as detection_error:
            print(f"❌ Detection error: {detection_error}")
            traceback.print_exc()
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

def test_model_file():
    """Test model file integrity"""
    print("\n🔍 TESTING MODEL FILE")
    print("=" * 30)
    
    model_path = "models/emotion_detection_83.6_percent.pt"
    
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        print("📁 Please ensure the model file is in the correct location")
        return False
    
    file_size = os.path.getsize(model_path)
    print(f"✅ Model file found: {model_path}")
    print(f"📊 File size: {file_size:,} bytes")
    
    # Check if file size is reasonable (YOLOv8 models are typically 6-50MB)
    if file_size < 1_000_000:  # Less than 1MB
        print("⚠️ WARNING: Model file seems too small")
    elif file_size > 100_000_000:  # More than 100MB
        print("⚠️ WARNING: Model file seems too large")
    else:
        print("✅ Model file size looks reasonable")
    
    return True

def test_yolo_import():
    """Test YOLOv8 import"""
    print("\n🔍 TESTING YOLO IMPORT")
    print("=" * 30)
    
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics YOLO imported successfully")
        
        # Test basic YOLO functionality
        print("🧪 Testing YOLO functionality...")
        model_path = "models/emotion_detection_83.6_percent.pt"
        
        if os.path.exists(model_path):
            model = YOLO(model_path)
            print("✅ YOLO model loaded successfully")
            
            # Test prediction
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            results = model.predict(dummy_image, verbose=False, conf=0.1)
            print("✅ YOLO prediction test successful")
            
            return True
        else:
            print("❌ Model file not found for YOLO test")
            return False
            
    except ImportError as e:
        print(f"❌ YOLO import failed: {e}")
        print("🔧 Try: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ YOLO test failed: {e}")
        return False

def quick_fix_detection():
    """Apply quick fixes to common detection issues"""
    print("\n🔧 APPLYING QUICK FIXES")
    print("=" * 30)
    
    fixes_applied = []
    
    # Fix 1: Check and create models directory
    if not os.path.exists("models"):
        os.makedirs("models")
        fixes_applied.append("Created models directory")
        print("✅ Created models directory")
    
    # Fix 2: Check model file permissions
    model_path = "models/emotion_detection_83.6_percent.pt"
    if os.path.exists(model_path):
        try:
            with open(model_path, 'rb') as f:
                f.read(1024)  # Try to read first 1KB
            fixes_applied.append("Verified model file is readable")
            print("✅ Model file is readable")
        except Exception as e:
            print(f"❌ Model file read error: {e}")
    
    # Fix 3: Clear any cached detection state
    try:
        import sys
        modules_to_clear = [m for m in sys.modules.keys() if 'detection' in m]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        fixes_applied.append("Cleared detection module cache")
        print("✅ Cleared detection module cache")
    except Exception as e:
        print(f"⚠️ Cache clear warning: {e}")
    
    return fixes_applied

def main():
    """Run emergency detection fix"""
    print("🚨 EMERGENCY FACIAL EXPRESSION DETECTION FIX")
    print("=" * 60)
    print(f"📅 Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Working Directory: {os.getcwd()}")
    
    # Step 1: Apply quick fixes
    fixes = quick_fix_detection()
    
    # Step 2: Test model file
    model_ok = test_model_file()
    
    # Step 3: Test YOLO import
    yolo_ok = test_yolo_import()
    
    # Step 4: Test basic detection
    detection_ok = test_basic_detection()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 EMERGENCY FIX SUMMARY")
    print("=" * 60)
    
    print(f"🔧 Fixes applied: {len(fixes)}")
    for fix in fixes:
        print(f"   ✅ {fix}")
    
    print(f"\n📊 Test Results:")
    print(f"   Model file: {'✅ OK' if model_ok else '❌ FAILED'}")
    print(f"   YOLO import: {'✅ OK' if yolo_ok else '❌ FAILED'}")
    print(f"   Detection: {'✅ OK' if detection_ok else '❌ FAILED'}")
    
    if detection_ok:
        print("\n🎉 SUCCESS: Detection system is working!")
        print("💡 You can now use facial expression detection")
    else:
        print("\n❌ DETECTION STILL FAILING")
        print("🔧 Possible solutions:")
        print("   1. Ensure emotion_detection_83.6_percent.pt is in models/ folder")
        print("   2. Check model file integrity (re-download if needed)")
        print("   3. Install/update ultralytics: pip install --upgrade ultralytics")
        print("   4. Restart the application")
        print("   5. Check console output for specific error messages")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Fix interrupted by user")
    except Exception as e:
        print(f"\n❌ Emergency fix failed: {e}")
        traceback.print_exc()
