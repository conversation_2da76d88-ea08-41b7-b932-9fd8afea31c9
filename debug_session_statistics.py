"""
Debug Session Statistics Issue
Investigates why session statistics are not updating properly
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_session_statistics():
    """Debug session statistics issue"""
    print("🔍 DEBUGGING SESSION STATISTICS ISSUE")
    print("=" * 50)
    
    try:
        from utils.database_integration import get_database
        
        # Get database instance
        db = get_database()
        print(f"✅ Database connected: {db.db_path}")
        print(f"🆔 Current session ID: {db.session_id}")
        
        # Check detection_sessions table structure
        print("\n📋 DETECTION_SESSIONS TABLE STRUCTURE:")
        print("-" * 40)
        
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        # Get table info
        cursor.execute("PRAGMA table_info(detection_sessions)")
        columns = cursor.fetchall()
        
        print("Column structure:")
        for i, col in enumerate(columns):
            print(f"  {i}: {col[1]} ({col[2]}) - {col}")
        
        # Check current session data
        print(f"\n📊 CURRENT SESSION DATA:")
        print("-" * 30)
        
        cursor.execute("SELECT * FROM detection_sessions WHERE session_id = ?", (db.session_id,))
        session_data = cursor.fetchone()
        
        if session_data:
            print("Raw session data:")
            for i, value in enumerate(session_data):
                col_name = columns[i][1] if i < len(columns) else f"col_{i}"
                print(f"  {i}: {col_name} = {value}")
        else:
            print("❌ No session data found!")
            
            # Check if session exists at all
            cursor.execute("SELECT COUNT(*) FROM detection_sessions")
            total_sessions = cursor.fetchone()[0]
            print(f"Total sessions in database: {total_sessions}")
            
            if total_sessions > 0:
                cursor.execute("SELECT session_id, start_time FROM detection_sessions ORDER BY start_time DESC LIMIT 5")
                recent_sessions = cursor.fetchall()
                print("Recent sessions:")
                for session in recent_sessions:
                    print(f"  - {session[0]} (started: {session[1]})")
        
        # Check detection counts by session
        print(f"\n📊 DETECTION COUNTS BY SESSION:")
        print("-" * 35)
        
        tables = ['age_detections', 'object_detections', 'expression_detections', 'anomaly_detections']
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (db.session_id,))
            count = cursor.fetchone()[0]
            print(f"  {table}: {count}")
        
        # Check total detections across all sessions
        print(f"\n📊 TOTAL DETECTIONS (ALL SESSIONS):")
        print("-" * 35)
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            total_count = cursor.fetchone()[0]
            print(f"  {table}: {total_count:,}")
        
        # Test session summary method
        print(f"\n🧪 TESTING SESSION SUMMARY METHOD:")
        print("-" * 35)
        
        summary = db.get_session_summary()
        if summary:
            print("Session summary result:")
            for key, value in summary.items():
                print(f"  {key}: {value}")
        else:
            print("❌ Session summary returned empty!")
        
        # Test logging a new detection
        print(f"\n🧪 TESTING NEW DETECTION LOGGING:")
        print("-" * 35)
        
        # Get current session stats before logging
        cursor.execute("SELECT total_detections, total_anomalies FROM detection_sessions WHERE session_id = ?", (db.session_id,))
        before_stats = cursor.fetchone()
        print(f"Before logging: total_detections={before_stats[0] if before_stats else 'None'}, total_anomalies={before_stats[1] if before_stats else 'None'}")
        
        # Log a test detection
        success = db.log_age_detection(
            age=25,
            age_range="Young Adult (20-30)",
            confidence=0.85,
            model_used="Test Model"
        )
        print(f"Test detection logged: {'✅' if success else '❌'}")
        
        # Get session stats after logging
        cursor.execute("SELECT total_detections, total_anomalies FROM detection_sessions WHERE session_id = ?", (db.session_id,))
        after_stats = cursor.fetchone()
        print(f"After logging: total_detections={after_stats[0] if after_stats else 'None'}, total_anomalies={after_stats[1] if after_stats else 'None'}")
        
        # Test session summary again
        summary_after = db.get_session_summary()
        if summary_after:
            print("Session summary after logging:")
            for key, value in summary_after.items():
                print(f"  {key}: {value}")
        else:
            print("❌ Session summary still empty after logging!")
        
        conn.close()
        
        # Identify the issue
        print(f"\n🔍 ISSUE ANALYSIS:")
        print("-" * 20)
        
        if not session_data:
            print("❌ ISSUE: Session not found in detection_sessions table")
            print("   SOLUTION: Session initialization failed")
        elif before_stats and after_stats and after_stats[0] > before_stats[0]:
            print("✅ Session statistics are updating correctly")
            if not summary or summary.get('total_detections') is None:
                print("❌ ISSUE: get_session_summary method has a bug")
                print("   SOLUTION: Fix column indexing in get_session_summary")
        else:
            print("❌ ISSUE: Session statistics not updating")
            print("   SOLUTION: Fix _insert_detection method")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_session_statistics():
    """Fix session statistics issues"""
    print("\n🔧 FIXING SESSION STATISTICS ISSUES")
    print("=" * 40)
    
    try:
        from utils.database_integration import get_database
        
        db = get_database()
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        # Check if current session exists
        cursor.execute("SELECT COUNT(*) FROM detection_sessions WHERE session_id = ?", (db.session_id,))
        session_exists = cursor.fetchone()[0] > 0
        
        if not session_exists:
            print(f"🔧 Creating missing session: {db.session_id}")
            cursor.execute('''
                INSERT INTO detection_sessions (session_id, start_time, total_detections, total_anomalies, status)
                VALUES (?, ?, 0, 0, 'ACTIVE')
            ''', (db.session_id, datetime.now()))
            print("✅ Session created")
        
        # Update session statistics based on actual detection counts
        print("🔧 Recalculating session statistics...")
        
        tables = ['age_detections', 'object_detections', 'expression_detections', 'anomaly_detections']
        total_detections = 0
        total_anomalies = 0
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (db.session_id,))
            count = cursor.fetchone()[0]
            total_detections += count
            
            if table == 'anomaly_detections':
                total_anomalies = count
        
        # Update session with correct counts
        cursor.execute('''
            UPDATE detection_sessions 
            SET total_detections = ?, total_anomalies = ?
            WHERE session_id = ?
        ''', (total_detections, total_anomalies, db.session_id))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Session statistics updated:")
        print(f"   Total detections: {total_detections}")
        print(f"   Total anomalies: {total_anomalies}")
        
        # Test session summary again
        summary = db.get_session_summary()
        if summary and summary.get('total_detections') is not None:
            print("✅ Session summary now working correctly!")
            return True
        else:
            print("❌ Session summary still not working")
            return False
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return False

def main():
    """Main debug function"""
    print("🔍 SESSION STATISTICS DEBUGGER")
    print("=" * 60)
    print("This script will investigate why session statistics are not updating.\n")
    
    # Debug the issue
    debug_success = debug_session_statistics()
    
    if debug_success:
        # Try to fix the issue
        fix_success = fix_session_statistics()
        
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS:")
        print(f"Debug completed: {'✅' if debug_success else '❌'}")
        print(f"Fix applied: {'✅' if fix_success else '❌'}")
        
        if fix_success:
            print("\n🎉 SESSION STATISTICS FIXED!")
            print("✅ Session tracking should now work correctly")
            print("✅ Dashboard should show accurate session statistics")
        else:
            print("\n⚠️ SESSION STATISTICS STILL NEED ATTENTION")
            print("Please check the debug output above for specific issues")
    
    return debug_success

if __name__ == "__main__":
    main()
