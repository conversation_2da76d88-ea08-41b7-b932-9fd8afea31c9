"""
Critical Dashboard Issues Diagnosis
Investigate database connections, delete operations, and refresh functionality
"""

import sqlite3
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_database_files():
    """Check all database files in the project"""
    print("🔍 CHECKING DATABASE FILES")
    print("=" * 60)
    
    # Look for all .db files
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                full_path = os.path.join(root, file)
                db_files.append(full_path)
    
    print(f"Found {len(db_files)} database files:")
    for db_file in db_files:
        size = os.path.getsize(db_file) if os.path.exists(db_file) else 0
        print(f"  📁 {db_file} ({size:,} bytes)")
    
    return db_files

def analyze_database_contents(db_path):
    """Analyze database contents and structure"""
    print(f"\n📊 ANALYZING DATABASE: {db_path}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"Tables found: {len(tables)}")
        
        total_records = 0
        for table_name, in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            total_records += count
            print(f"  📋 {table_name}: {count:,} records")
            
            # Show sample data for detection tables
            if 'detection' in table_name:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                samples = cursor.fetchall()
                if samples:
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in cursor.fetchall()]
                    print(f"    Sample data (columns: {', '.join(columns[:5])}...):")
                    for i, sample in enumerate(samples[:2]):
                        print(f"      Row {i+1}: {sample[:5]}...")
        
        print(f"\n📈 TOTAL RECORDS: {total_records:,}")
        
        conn.close()
        return total_records
        
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        return 0

def test_direct_database_operations():
    """Test direct database operations"""
    print("\n🔧 TESTING DIRECT DATABASE OPERATIONS")
    print("=" * 60)
    
    db_path = "detection_results.db"
    
    try:
        # Test connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("✅ Database connection successful")
        
        # Get current counts
        detection_tables = ['age_detections', 'object_detections', 'expression_detections', 'anomaly_detections']
        original_counts = {}
        
        for table in detection_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                original_counts[table] = count
                print(f"📊 {table}: {count:,} records")
            except Exception as e:
                print(f"❌ Error counting {table}: {e}")
                original_counts[table] = 0
        
        # Test insert operation
        print("\n🧪 Testing INSERT operation...")
        test_timestamp = datetime.now().isoformat()
        
        try:
            cursor.execute("""
                INSERT INTO age_detections (timestamp, age, age_range, confidence, model_used, session_id)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (test_timestamp, 99, "(90-100)", 0.99, "TEST_MODEL", "test_session"))
            
            conn.commit()
            
            # Verify insert
            cursor.execute("SELECT COUNT(*) FROM age_detections WHERE age = 99")
            test_count = cursor.fetchone()[0]
            
            if test_count > 0:
                print("✅ INSERT operation successful")
                
                # Test delete operation
                print("🧪 Testing DELETE operation...")
                cursor.execute("DELETE FROM age_detections WHERE age = 99 AND model_used = 'TEST_MODEL'")
                deleted_rows = cursor.rowcount
                conn.commit()
                
                print(f"✅ DELETE operation successful - removed {deleted_rows} rows")
            else:
                print("❌ INSERT operation failed")
                
        except Exception as e:
            print(f"❌ Database operation error: {e}")
        
        # Verify final counts
        print("\n📊 Final counts:")
        for table in detection_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count:,} records")
            except Exception as e:
                print(f"❌ Error counting {table}: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False

def test_dashboard_database_connection():
    """Test dashboard database connection"""
    print("\n🎯 TESTING DASHBOARD DATABASE CONNECTION")
    print("=" * 60)
    
    try:
        from gui.enhanced_dashboard import StreamlinedDashboard
        
        # Create dashboard instance
        dashboard = StreamlinedDashboard()
        
        # Check database connection
        if hasattr(dashboard, 'enhanced_db') and dashboard.enhanced_db:
            db_path = dashboard.enhanced_db.db_path
            print(f"✅ Dashboard connected to: {db_path}")
            
            # Test getting data through dashboard
            counts = dashboard.get_current_detection_counts()
            print(f"📊 Dashboard counts: {counts}")
            
            # Test refresh
            print("🔄 Testing dashboard refresh...")
            dashboard.refresh_data()
            
            total_after_refresh = sum(len(data) for data in dashboard.current_data.values())
            print(f"📊 Records after refresh: {total_after_refresh:,}")
            
            return True, db_path
        else:
            print("❌ Dashboard database connection not found")
            return False, None
            
    except Exception as e:
        print(f"❌ Dashboard connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_delete_operation_simulation():
    """Simulate and test delete operation"""
    print("\n🗑️ TESTING DELETE OPERATION SIMULATION")
    print("=" * 60)
    
    try:
        from gui.enhanced_dashboard import StreamlinedDashboard
        
        dashboard = StreamlinedDashboard()
        
        if not dashboard.enhanced_db:
            print("❌ No database connection")
            return False
        
        # Get initial counts
        initial_counts = {}
        for det_type in ['age', 'object', 'expression', 'anomaly']:
            records = dashboard.enhanced_db.get_recent_detections(det_type, limit=50000)
            initial_counts[det_type] = len(records)
            print(f"📊 Initial {det_type}: {initial_counts[det_type]:,} records")
        
        # Simulate delete operation (delete records with very low confidence)
        print("\n🧪 Simulating delete operation...")
        
        table_map = {
            'age': 'age_detections',
            'object': 'object_detections', 
            'expression': 'expression_detections',
            'anomaly': 'anomaly_detections'
        }
        
        total_deleted = 0
        
        for det_type, table in table_map.items():
            try:
                # Delete records with confidence < 0.01 (very low confidence test records)
                conn = sqlite3.connect(dashboard.enhanced_db.db_path)
                cursor = conn.cursor()
                
                # First check how many would be deleted
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE confidence < 0.01")
                would_delete = cursor.fetchone()[0]
                
                if would_delete > 0:
                    print(f"   {det_type}: Would delete {would_delete} low-confidence records")
                    
                    # Actually delete them
                    cursor.execute(f"DELETE FROM {table} WHERE confidence < 0.01")
                    deleted = cursor.rowcount
                    total_deleted += deleted
                    
                    conn.commit()
                    print(f"   {det_type}: Actually deleted {deleted} records")
                else:
                    print(f"   {det_type}: No low-confidence records to delete")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ Error deleting from {det_type}: {e}")
        
        print(f"\n📊 Total deleted: {total_deleted} records")
        
        # Get final counts
        final_counts = {}
        for det_type in ['age', 'object', 'expression', 'anomaly']:
            records = dashboard.enhanced_db.get_recent_detections(det_type, limit=50000)
            final_counts[det_type] = len(records)
            print(f"📊 Final {det_type}: {final_counts[det_type]:,} records")
        
        # Test dashboard refresh after deletion
        print("\n🔄 Testing dashboard refresh after deletion...")
        dashboard.refresh_data()
        
        refresh_total = sum(len(data) for data in dashboard.current_data.values())
        print(f"📊 Dashboard total after refresh: {refresh_total:,}")
        
        return total_deleted > 0
        
    except Exception as e:
        print(f"❌ Delete operation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnosis function"""
    print("🚨 CRITICAL DASHBOARD ISSUES DIAGNOSIS")
    print("=" * 80)
    
    # Step 1: Check database files
    db_files = check_database_files()
    
    # Step 2: Analyze main database
    main_db = "detection_results.db"
    if os.path.exists(main_db):
        total_records = analyze_database_contents(main_db)
    else:
        print(f"❌ Main database {main_db} not found!")
        return
    
    # Step 3: Test direct database operations
    db_ops_ok = test_direct_database_operations()
    
    # Step 4: Test dashboard connection
    dashboard_ok, dashboard_db_path = test_dashboard_database_connection()
    
    # Step 5: Test delete operation
    delete_ok = test_delete_operation_simulation()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 DIAGNOSIS SUMMARY")
    print("=" * 80)
    print(f"Database Files Found: {len(db_files)}")
    print(f"Total Records in Main DB: {total_records:,}")
    print(f"Direct DB Operations: {'✅ WORKING' if db_ops_ok else '❌ FAILED'}")
    print(f"Dashboard DB Connection: {'✅ WORKING' if dashboard_ok else '❌ FAILED'}")
    print(f"Delete Operations: {'✅ WORKING' if delete_ok else '❌ FAILED'}")
    
    if dashboard_db_path:
        print(f"Dashboard DB Path: {dashboard_db_path}")
        if dashboard_db_path != main_db:
            print("⚠️ WARNING: Dashboard using different database file!")
    
    if all([db_ops_ok, dashboard_ok, delete_ok]):
        print("\n✅ All core functionality appears to be working")
        print("🔍 Issues may be in UI update mechanisms or event handling")
    else:
        print("\n❌ Critical database operation issues found")
        print("🔧 Database connection or transaction problems need fixing")

if __name__ == "__main__":
    main()
