#!/usr/bin/env python3
"""
Camera Color Fix Tool
Permanently fixes grayscale camera issues by configuring camera settings
"""

import cv2
import numpy as np
import tkinter as tk
from tkinter import messagebox, ttk
import time
import subprocess
import sys
import os

class CameraColorFixer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎨 Camera Color Fix Tool")
        self.root.geometry("900x700")
        self.root.configure(bg='#2E86AB')
        
        self.video_cap = None
        self.is_running = False
        self.current_backend = cv2.CAP_ANY
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the fix tool UI"""
        # Title
        title_label = tk.Label(self.root, text="🎨 Camera Color Fix Tool", 
                              font=('Arial', 18, 'bold'), bg='#2E86AB', fg='white')
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(self.root, text="Fix grayscale camera issues permanently", 
                                 font=('Arial', 12), bg='#2E86AB', fg='lightgray')
        subtitle_label.pack(pady=5)
        
        # Main container
        main_frame = tk.Frame(self.root, bg='white', relief='solid', bd=2)
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Status display
        status_frame = tk.Frame(main_frame, bg='white')
        status_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(status_frame, text="📊 Camera Status:", font=('Arial', 12, 'bold'), 
                bg='white').pack(anchor='w')
        
        self.status_text = tk.Text(status_frame, height=8, font=('Courier', 9))
        self.status_text.pack(fill='x', pady=5)
        
        # Control buttons
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        # Row 1
        row1 = tk.Frame(button_frame, bg='white')
        row1.pack(fill='x', pady=5)
        
        self.scan_btn = tk.Button(row1, text="🔍 Scan Camera", 
                                 font=('Arial', 11, 'bold'), bg='#007bff', fg='white',
                                 command=self.scan_camera, padx=15, pady=5)
        self.scan_btn.pack(side='left', padx=5)
        
        self.fix_btn = tk.Button(row1, text="🔧 Apply Color Fix", 
                                font=('Arial', 11, 'bold'), bg='#28a745', fg='white',
                                command=self.apply_color_fix, padx=15, pady=5)
        self.fix_btn.pack(side='left', padx=5)
        
        self.test_btn = tk.Button(row1, text="🎥 Test Camera", 
                                 font=('Arial', 11, 'bold'), bg='#ffc107', fg='black',
                                 command=self.test_camera, padx=15, pady=5)
        self.test_btn.pack(side='left', padx=5)
        
        # Row 2
        row2 = tk.Frame(button_frame, bg='white')
        row2.pack(fill='x', pady=5)
        
        self.driver_btn = tk.Button(row2, text="🔄 Update Drivers", 
                                   font=('Arial', 11, 'bold'), bg='#6f42c1', fg='white',
                                   command=self.update_drivers, padx=15, pady=5)
        self.driver_btn.pack(side='left', padx=5)
        
        self.settings_btn = tk.Button(row2, text="⚙️ Camera Settings", 
                                     font=('Arial', 11, 'bold'), bg='#fd7e14', fg='white',
                                     command=self.open_camera_settings, padx=15, pady=5)
        self.settings_btn.pack(side='left', padx=5)
        
        self.reset_btn = tk.Button(row2, text="🔄 Reset Camera", 
                                  font=('Arial', 11, 'bold'), bg='#dc3545', fg='white',
                                  command=self.reset_camera, padx=15, pady=5)
        self.reset_btn.pack(side='left', padx=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill='x', padx=10, pady=5)
        
        # Instructions
        instructions_frame = tk.Frame(main_frame, bg='#f8f9fa', relief='solid', bd=1)
        instructions_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(instructions_frame, text="📋 Instructions:", 
                font=('Arial', 12, 'bold'), bg='#f8f9fa').pack(anchor='w', padx=10, pady=5)
        
        instructions = """
1. Click 'Scan Camera' to detect your camera and check color support
2. Click 'Apply Color Fix' to configure camera for color mode
3. Click 'Test Camera' to verify the fix worked
4. If still grayscale, try 'Update Drivers' or 'Camera Settings'
5. Use 'Reset Camera' as last resort to restore defaults
        """
        
        tk.Label(instructions_frame, text=instructions, font=('Arial', 10), 
                bg='#f8f9fa', justify='left').pack(anchor='w', padx=10, pady=5)
        
    def log_status(self, message):
        """Log status message"""
        timestamp = time.strftime('%H:%M:%S')
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def scan_camera(self):
        """Scan camera and detect issues"""
        self.log_status("🔍 Starting camera scan...")
        self.progress.start()
        
        try:
            # Test different backends
            backends = [
                (cv2.CAP_DSHOW, "DirectShow"),
                (cv2.CAP_MSMF, "Media Foundation"),
                (cv2.CAP_ANY, "Auto-detect")
            ]
            
            best_backend = None
            best_color_score = 0
            
            for backend_id, backend_name in backends:
                self.log_status(f"Testing {backend_name} backend...")
                
                try:
                    cap = cv2.VideoCapture(0, backend_id)
                    if cap.isOpened():
                        ret, frame = cap.read()
                        if ret and frame is not None:
                            # Analyze color
                            color_score = self.analyze_color_quality(frame)
                            self.log_status(f"{backend_name}: Color score = {color_score:.2f}")
                            
                            if color_score > best_color_score:
                                best_color_score = color_score
                                best_backend = (backend_id, backend_name)
                    cap.release()
                except Exception as e:
                    self.log_status(f"{backend_name}: Failed - {e}")
            
            if best_backend:
                self.current_backend = best_backend[0]
                self.log_status(f"✅ Best backend: {best_backend[1]} (score: {best_color_score:.2f})")
                
                if best_color_score < 5.0:
                    self.log_status("⚠️ ISSUE DETECTED: Camera is in grayscale mode!")
                    self.log_status("🔧 Recommendation: Apply color fix")
                else:
                    self.log_status("✅ Camera appears to be working in color mode")
            else:
                self.log_status("❌ No working camera detected")
                
        except Exception as e:
            self.log_status(f"❌ Scan failed: {e}")
        finally:
            self.progress.stop()
            
    def analyze_color_quality(self, frame):
        """Analyze frame for color quality"""
        if len(frame.shape) != 3:
            return 0.0
            
        b, g, r = cv2.split(frame)
        
        # Calculate channel differences
        bg_diff = np.abs(b.astype(float) - g.astype(float)).mean()
        gr_diff = np.abs(g.astype(float) - r.astype(float)).mean()
        br_diff = np.abs(b.astype(float) - r.astype(float)).mean()
        
        return bg_diff + gr_diff + br_diff
        
    def apply_color_fix(self):
        """Apply comprehensive color fix"""
        self.log_status("🔧 Applying comprehensive color fix...")
        self.progress.start()
        
        try:
            cap = cv2.VideoCapture(0, self.current_backend)
            if not cap.isOpened():
                self.log_status("❌ Cannot open camera")
                return
                
            self.log_status("📷 Camera opened, applying color settings...")
            
            # Apply all color fixes
            fixes_applied = 0
            
            # Fix 1: Force RGB conversion
            try:
                cap.set(cv2.CAP_PROP_CONVERT_RGB, 1)
                self.log_status("✅ Enabled RGB conversion")
                fixes_applied += 1
            except:
                self.log_status("⚠️ RGB conversion not supported")
            
            # Fix 2: Disable monochrome
            try:
                cap.set(cv2.CAP_PROP_MONOCHROME, 0)
                self.log_status("✅ Disabled monochrome mode")
                fixes_applied += 1
            except:
                self.log_status("⚠️ Monochrome control not available")
            
            # Fix 3: Maximum saturation
            try:
                cap.set(cv2.CAP_PROP_SATURATION, 255)
                self.log_status("✅ Set maximum saturation")
                fixes_applied += 1
            except:
                self.log_status("⚠️ Saturation control not supported")
            
            # Fix 4: Optimal brightness/contrast
            try:
                cap.set(cv2.CAP_PROP_BRIGHTNESS, 140)
                cap.set(cv2.CAP_PROP_CONTRAST, 150)
                self.log_status("✅ Set optimal brightness/contrast")
                fixes_applied += 1
            except:
                self.log_status("⚠️ Brightness/contrast control not supported")
            
            # Fix 5: Auto white balance
            try:
                cap.set(cv2.CAP_PROP_AUTO_WB, 1)
                self.log_status("✅ Enabled auto white balance")
                fixes_applied += 1
            except:
                self.log_status("⚠️ White balance control not supported")
            
            # Test the fix
            ret, frame = cap.read()
            if ret:
                color_score = self.analyze_color_quality(frame)
                self.log_status(f"📊 Post-fix color score: {color_score:.2f}")
                
                if color_score > 5.0:
                    self.log_status("🎉 SUCCESS! Camera is now in color mode!")
                    messagebox.showinfo("Success", "✅ Camera color fix applied successfully!\n\nYour camera should now display in color.")
                else:
                    self.log_status("⚠️ Fix partially successful, may need additional steps")
                    messagebox.showwarning("Partial Success", f"Applied {fixes_applied} fixes, but camera may still need manual configuration.\n\nTry 'Camera Settings' or 'Update Drivers'.")
            
            cap.release()
            self.log_status(f"✅ Color fix complete - {fixes_applied} settings applied")
            
        except Exception as e:
            self.log_status(f"❌ Color fix failed: {e}")
        finally:
            self.progress.stop()
            
    def test_camera(self):
        """Test camera with current settings"""
        self.log_status("🎥 Testing camera...")
        
        try:
            cap = cv2.VideoCapture(0, self.current_backend)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    color_score = self.analyze_color_quality(frame)
                    self.log_status(f"📊 Current color score: {color_score:.2f}")
                    
                    if color_score > 5.0:
                        self.log_status("✅ Camera is working in COLOR mode!")
                        messagebox.showinfo("Test Result", "✅ SUCCESS!\n\nYour camera is working in color mode.")
                    else:
                        self.log_status("⚠️ Camera is still in GRAYSCALE mode")
                        messagebox.showwarning("Test Result", "⚠️ Camera is still in grayscale mode.\n\nTry applying the color fix or updating drivers.")
                else:
                    self.log_status("❌ Cannot read from camera")
                cap.release()
            else:
                self.log_status("❌ Cannot open camera")
                
        except Exception as e:
            self.log_status(f"❌ Test failed: {e}")
            
    def update_drivers(self):
        """Open Device Manager to update camera drivers"""
        self.log_status("🔄 Opening Device Manager for driver update...")
        
        try:
            subprocess.run(['devmgmt.msc'], shell=True)
            messagebox.showinfo("Driver Update", 
                               "Device Manager opened.\n\n"
                               "1. Find your camera under 'Cameras' or 'Imaging devices'\n"
                               "2. Right-click → 'Update driver'\n"
                               "3. Choose 'Search automatically for drivers'\n"
                               "4. Restart your computer after update")
        except Exception as e:
            self.log_status(f"❌ Cannot open Device Manager: {e}")
            
    def open_camera_settings(self):
        """Open Windows Camera app settings"""
        self.log_status("⚙️ Opening Camera app...")
        
        try:
            subprocess.run(['start', 'ms-windows-store://pdp/?productid=9WZDNCRFJBBG'], shell=True)
            messagebox.showinfo("Camera Settings", 
                               "Windows Camera app should open.\n\n"
                               "1. Click the Settings gear icon\n"
                               "2. Look for 'Video quality' or 'Camera options'\n"
                               "3. Ensure color mode is enabled\n"
                               "4. Disable any 'Grayscale' or 'Monochrome' options")
        except Exception as e:
            self.log_status(f"❌ Cannot open Camera app: {e}")
            
    def reset_camera(self):
        """Reset camera to defaults"""
        if messagebox.askyesno("Reset Camera", "This will reset your camera to default settings.\n\nContinue?"):
            self.log_status("🔄 Resetting camera to defaults...")
            
            try:
                cap = cv2.VideoCapture(0)
                if cap.isOpened():
                    # Reset to defaults
                    cap.set(cv2.CAP_PROP_BRIGHTNESS, 128)
                    cap.set(cv2.CAP_PROP_CONTRAST, 128)
                    cap.set(cv2.CAP_PROP_SATURATION, 128)
                    cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.75)
                    cap.set(cv2.CAP_PROP_AUTO_WB, 1)
                    
                    self.log_status("✅ Camera reset to defaults")
                    cap.release()
                else:
                    self.log_status("❌ Cannot open camera for reset")
                    
            except Exception as e:
                self.log_status(f"❌ Reset failed: {e}")
                
    def run(self):
        """Run the fix tool"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """Handle window closing"""
        if self.video_cap:
            self.video_cap.release()
        self.root.destroy()

if __name__ == "__main__":
    print("🎨 Starting Camera Color Fix Tool...")
    app = CameraColorFixer()
    app.run()
