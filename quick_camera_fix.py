#!/usr/bin/env python3
"""
Quick Camera Grayscale Fix
Direct approach to fix grayscale camera issues
"""

import cv2
import numpy as np
import time

def test_camera_backends():
    """Test different camera backends"""
    print("🔍 Testing camera backends...")
    
    backends = [
        (cv2.CAP_DSHOW, "DirectShow (Windows)"),
        (cv2.CAP_MSMF, "Media Foundation (Windows)"),
        (cv2.CAP_V4L2, "Video4Linux2"),
        (cv2.CAP_ANY, "Auto-detect")
    ]
    
    working_backends = []
    
    for backend_id, backend_name in backends:
        print(f"\n📷 Testing {backend_name}...")
        
        try:
            cap = cv2.VideoCapture(0, backend_id)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    # Analyze color
                    color_score = analyze_color_quality(frame)
                    print(f"✅ {backend_name}: Working (Color score: {color_score:.2f})")
                    
                    if len(frame.shape) == 3:
                        print(f"   📊 Frame shape: {frame.shape} (3 channels)")
                        b, g, r = cv2.split(frame)
                        print(f"   📊 Channel means: B={b.mean():.1f}, G={g.mean():.1f}, R={r.mean():.1f}")
                    else:
                        print(f"   ⚠️ Frame shape: {frame.shape} (grayscale)")
                    
                    working_backends.append((backend_id, backend_name, color_score))
                else:
                    print(f"❌ {backend_name}: Cannot read frame")
                cap.release()
            else:
                print(f"❌ {backend_name}: Cannot open camera")
                
        except Exception as e:
            print(f"❌ {backend_name}: Error - {e}")
    
    return working_backends

def analyze_color_quality(frame):
    """Analyze frame for color quality"""
    if len(frame.shape) != 3:
        return 0.0
        
    b, g, r = cv2.split(frame)
    
    # Calculate channel differences
    bg_diff = np.abs(b.astype(float) - g.astype(float)).mean()
    gr_diff = np.abs(g.astype(float) - r.astype(float)).mean()
    br_diff = np.abs(b.astype(float) - r.astype(float)).mean()
    
    return bg_diff + gr_diff + br_diff

def apply_aggressive_color_fix(backend_id, backend_name):
    """Apply aggressive color fix"""
    print(f"\n🔧 Applying aggressive color fix with {backend_name}...")
    
    try:
        cap = cv2.VideoCapture(0, backend_id)
        if not cap.isOpened():
            print(f"❌ Cannot open camera with {backend_name}")
            return False
        
        print("📷 Camera opened, applying color settings...")
        
        # CRITICAL COLOR FIXES
        fixes = [
            (cv2.CAP_PROP_CONVERT_RGB, 1, "Force RGB conversion"),
            (cv2.CAP_PROP_MONOCHROME, 0, "Disable monochrome"),
            (cv2.CAP_PROP_SATURATION, 255, "Maximum saturation"),
            (cv2.CAP_PROP_CONTRAST, 200, "High contrast"),
            (cv2.CAP_PROP_BRIGHTNESS, 150, "Optimal brightness"),
            (cv2.CAP_PROP_AUTO_WB, 1, "Auto white balance"),
            (cv2.CAP_PROP_GAMMA, 100, "Gamma correction"),
            (cv2.CAP_PROP_GAIN, 50, "Gain adjustment"),
        ]
        
        fixes_applied = 0
        for prop, value, description in fixes:
            try:
                result = cap.set(prop, value)
                if result:
                    print(f"✅ {description}: Set to {value}")
                    fixes_applied += 1
                else:
                    print(f"⚠️ {description}: Not supported")
            except Exception as e:
                print(f"❌ {description}: Failed - {e}")
        
        # Try different pixel formats
        print("\n🎨 Trying different pixel formats...")
        formats = [
            (cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'), "MJPEG"),
            (cv2.VideoWriter_fourcc('Y', 'U', 'Y', 'V'), "YUYV"),
            (cv2.VideoWriter_fourcc('U', 'Y', 'V', 'Y'), "UYVY"),
        ]
        
        for fmt, fmt_name in formats:
            try:
                if cap.set(cv2.CAP_PROP_FOURCC, fmt):
                    print(f"✅ Set pixel format to {fmt_name}")
                    break
            except:
                print(f"⚠️ {fmt_name} format not supported")
        
        # Test the result
        print("\n📊 Testing color fix result...")
        ret, frame = cap.read()
        if ret:
            color_score = analyze_color_quality(frame)
            print(f"📊 Post-fix color score: {color_score:.2f}")
            
            if color_score > 5.0:
                print("🎉 SUCCESS! Camera is now in COLOR mode!")
                
                # Show a preview window
                print("📺 Showing 5-second preview...")
                for i in range(150):  # 5 seconds at 30fps
                    ret, frame = cap.read()
                    if ret:
                        cv2.imshow('Color Test - Press ESC to close', frame)
                        if cv2.waitKey(1) & 0xFF == 27:  # ESC key
                            break
                cv2.destroyAllWindows()
                
                cap.release()
                return True
            else:
                print("⚠️ Still in grayscale mode")
        
        cap.release()
        print(f"✅ Applied {fixes_applied} color fixes")
        return False
        
    except Exception as e:
        print(f"❌ Color fix failed: {e}")
        return False

def main():
    """Main function"""
    print("🎥 Quick Camera Grayscale Fix Tool")
    print("=" * 50)
    
    # Step 1: Test backends
    working_backends = test_camera_backends()
    
    if not working_backends:
        print("\n❌ No working camera backends found!")
        print("🔧 Troubleshooting steps:")
        print("1. Check camera connection")
        print("2. Close other camera applications")
        print("3. Try different USB port")
        print("4. Update camera drivers")
        return
    
    print(f"\n✅ Found {len(working_backends)} working backend(s)")
    
    # Step 2: Find best backend
    best_backend = max(working_backends, key=lambda x: x[2])  # Highest color score
    backend_id, backend_name, color_score = best_backend
    
    print(f"\n🏆 Best backend: {backend_name} (Color score: {color_score:.2f})")
    
    if color_score > 5.0:
        print("✅ Camera is already in COLOR mode!")
        print("🎉 No fix needed - your camera is working correctly!")
    else:
        print("⚠️ Camera is in GRAYSCALE mode - applying fix...")
        
        # Step 3: Apply fix
        success = apply_aggressive_color_fix(backend_id, backend_name)
        
        if not success:
            print("\n❌ Software fix unsuccessful")
            print("🔧 Manual solutions to try:")
            print("1. Open Windows Camera app")
            print("2. Look for Settings/Preferences")
            print("3. Find 'Color' or 'Grayscale' toggle")
            print("4. Disable 'Night mode' or 'Infrared' mode")
            print("5. Check for camera-specific software")
            print("6. Try reinstalling camera drivers")
            print("\n💡 Some cameras have hardware grayscale mode that cannot be changed via software")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
