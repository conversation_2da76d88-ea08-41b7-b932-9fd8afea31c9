# 🚨 Automated Anomaly Detection and Recording System

## Overview

This comprehensive anomaly detection system integrates seamlessly with your existing object detection application, providing automated detection, recording, and reporting of unusual events.

## 🎯 Key Features

### **Anomaly Detection Scope**
- **Object-based anomalies**: Unusual objects, unexpected items, or objects that shouldn't be present
- **YOLO integration**: Uses your existing `coco.names`, `yolov3.weights`, `yolov3.cfg` models
- **Smart classification**: Identifies suspicious objects, unknown items, and restricted area violations
- **Confidence-based detection**: Objects with low confidence scores trigger anomaly alerts

### **Automatic Recording System**
- **10-second recordings**: Automatically starts recording when anomalies are detected
- **Pre-recording buffer**: Captures 2 seconds before the anomaly for context
- **MP4 format**: High-quality video files with timestamp naming
- **Dedicated storage**: All recordings saved in `anomaly_recordings/` folder
- **Background processing**: Non-blocking recording that maintains real-time performance

### **Automated Report Generation**
- **Multiple formats**: Text (.txt), JSON (.json), and PDF (.pdf) reports
- **Comprehensive details**: Timestamp, anomaly type, confidence scores, object coordinates
- **Screenshots included**: Captures the exact moment of anomaly detection
- **Metadata tracking**: Camera settings, detection model info, system performance metrics

### **Integration & Performance**
- **Seamless compatibility**: Works alongside age detection and emotion detection systems
- **Real-time performance**: Maintains >3 FPS even during recording
- **Thread-safe operation**: Background recording doesn't block main detection loop
- **Automatic cleanup**: Removes files older than 30 days to manage storage

## 🛠️ Installation

### 1. Install Dependencies
```bash
python install_anomaly_dependencies.py
```

### 2. Verify YOLO Models
Ensure these files exist in your `models/` folder:
- `yolov3.weights`
- `yolov3.cfg`
- `coco.names`

### 3. Run Your Application
```bash
python main.py
```

## 🎮 Usage

### **Enable Anomaly Detection**
1. Start your camera
2. Click the **🚨 Anomaly** button to enable detection
3. The system will automatically monitor for anomalies

### **View Recordings**
- Click **📹 Recordings** button to view saved anomaly videos
- Click **📁 Open Folder** to access the recordings directory

### **View Reports**
- Click **📊 Reports** button to view generated reports
- Reports include detailed analysis and screenshots

### **Keyboard Shortcuts**
- `3` - Toggle anomaly detection on/off
- `Space` - Trigger expression detection (existing feature)

## 📊 Anomaly Categories

### **Suspicious Objects**
- `knife`, `scissors`, `baseball bat` - Potentially dangerous items

### **Unusual Indoor Objects**
- `airplane`, `boat`, `train`, `truck` - Vehicles in indoor settings
- `horse`, `cow`, `elephant` - Animals not typically indoors

### **Security Concerns**
- `suitcase`, `backpack` - Unattended bags (configurable)

### **Unknown Objects**
- Objects with confidence < 20% - Unrecognized items

### **Low Confidence Detections**
- Normal objects with confidence < 30% - Potential false positives

## 🔧 Configuration

### **Sensitivity Settings**
Edit `utils/anomaly_config.py`:
```python
# Adjust detection thresholds
self.anomaly_confidence_threshold = 0.3  # Lower = more sensitive
self.unknown_object_threshold = 0.2      # Very low confidence threshold
self.anomaly_sensitivity = 0.7           # Overall sensitivity (0.0-1.0)
```

### **Recording Settings**
```python
# Recording duration and quality
self.recording_duration = 10             # seconds
self.recording_fps = 30                  # frames per second
self.pre_recording_buffer = 2            # seconds before anomaly
```

### **File Management**
```python
# Automatic cleanup
self.cleanup_days = 30                   # Delete files older than 30 days
self.max_recordings = 100                # Maximum recordings to keep
```

## 📁 File Structure

```
your_project/
├── detection/
│   ├── anomaly_detector.py      # Core detection engine
│   ├── anomaly_system.py        # Integrated system coordinator
│   └── ...
├── recording/
│   └── anomaly_recorder.py      # Video recording system
├── reporting/
│   └── anomaly_reporter.py      # Report generation
├── utils/
│   └── anomaly_config.py        # Configuration settings
├── anomaly_recordings/          # Generated video files
├── anomaly_reports/             # Generated reports
└── models/                      # YOLO model files
    ├── yolov3.weights
    ├── yolov3.cfg
    └── coco.names
```

## 🚀 Technical Specifications

### **Performance**
- **Detection FPS**: Maintains >3 FPS during anomaly detection
- **Recording**: Background threaded recording (non-blocking)
- **Memory usage**: Optimized frame buffering
- **CPU usage**: Efficient YOLO processing with configurable intervals

### **Supported Formats**
- **Video**: MP4 (H.264 encoding)
- **Reports**: TXT, JSON, PDF
- **Images**: JPEG screenshots (95% quality)

### **System Requirements**
- **Python**: 3.7+
- **OpenCV**: 4.0+
- **NumPy**: 1.19+
- **ReportLab**: 3.5+ (for PDF reports)

## 🔍 Troubleshooting

### **Common Issues**

**"Anomaly system not available"**
- Ensure YOLO model files are in the `models/` folder
- Check that `install_anomaly_dependencies.py` ran successfully

**"No anomaly recordings found"**
- Enable anomaly detection first
- Trigger a test anomaly or wait for real detections

**"Failed to generate PDF reports"**
- Install ReportLab: `pip install reportlab`
- Check write permissions in the project directory

**Low detection performance**
- Reduce `detection_interval` in main window settings
- Adjust `anomaly_sensitivity` in configuration
- Ensure adequate lighting for camera

### **Debug Mode**
Enable detailed logging by setting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 Advanced Features

### **Custom Anomaly Rules**
Add custom anomaly detection rules in `anomaly_config.py`:
```python
# Add custom suspicious objects
self.suspicious_objects.add('your_custom_object')

# Define restricted areas (x1, y1, x2, y2)
self.restricted_areas = [(100, 100, 200, 200)]
```

### **Integration with External Systems**
The system provides hooks for integration:
- **Webhook notifications**: Add HTTP callbacks for anomalies
- **Database logging**: Store anomaly data in databases
- **Email alerts**: Send notifications for high-priority anomalies

## 🎯 Future Enhancements

- **Real-time streaming**: Live anomaly detection streaming
- **Machine learning**: Custom anomaly detection models
- **Mobile alerts**: Push notifications to mobile devices
- **Cloud storage**: Automatic backup of recordings and reports

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the configuration files
3. Enable debug logging for detailed error information
4. Ensure all dependencies are properly installed

---

**🎉 Your automated anomaly detection system is ready to protect your environment with intelligent monitoring, automatic recording, and comprehensive reporting!**
