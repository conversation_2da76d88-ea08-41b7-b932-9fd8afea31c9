Requirement already satisfied: ultralytics in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (8.3.59)
Requirement already satisfied: numpy>=1.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (1.26.4)
Requirement already satisfied: matplotlib>=3.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (3.9.4)
Requirement already satisfied: opencv-python>=4.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (*********)
Requirement already satisfied: pillow>=7.1.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (10.4.0)
Requirement already satisfied: pyyaml>=5.3.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (6.0.2)
Requirement already satisfied: requests>=2.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (2.32.3)
Requirement already satisfied: scipy>=1.4.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (1.13.1)
Requirement already satisfied: torch>=1.8.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (2.5.1)
Requirement already satisfied: torchvision>=0.9.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (0.20.1)
Requirement already satisfied: tqdm>=4.64.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (4.67.1)
Requirement already satisfied: psutil in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (6.1.1)
Requirement already satisfied: py-cpuinfo in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (9.0.0)
Requirement already satisfied: pandas>=1.1.4 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (2.2.3)
Requirement already satisfied: seaborn>=0.11.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (0.13.2)
Requirement already satisfied: ultralytics-thop>=2.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from ultralytics) (2.0.13)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (1.3.0)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (4.55.3)
Requirement already satisfied: kiwisolver>=1.3.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (1.4.7)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (24.2)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (3.2.1)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (2.9.0.post0)
Requirement already satisfied: importlib-resources>=3.2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from matplotlib>=3.3.0->ultralytics) (6.5.2)
Requirement already satisfied: zipp>=3.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from importlib-resources>=3.2.0->matplotlib>=3.3.0->ultralytics) (3.21.0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pandas>=1.1.4->ultralytics) (2024.2)
Requirement already satisfied: tzdata>=2022.7 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pandas>=1.1.4->ultralytics) (2024.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from python-dateutil>=2.7->matplotlib>=3.3.0->ultralytics) (1.17.0)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.23.0->ultralytics) (3.4.0)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.23.0->ultralytics) (3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.23.0->ultralytics) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.23.0->ultralytics) (2024.12.14)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from torch>=1.8.0->ultralytics) (3.16.1)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from torch>=1.8.0->ultralytics) (4.12.2)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from torch>=1.8.0->ultralytics) (3.2.1)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from torch>=1.8.0->ultralytics) (3.1.4)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from torch>=1.8.0->ultralytics) (2024.12.0)
Requirement already satisfied: sympy==1.13.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from torch>=1.8.0->ultralytics) (1.13.1)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from sympy==1.13.1->torch>=1.8.0->ultralytics) (1.3.0)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from tqdm>=4.64.0->ultralytics) (0.4.6)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)
