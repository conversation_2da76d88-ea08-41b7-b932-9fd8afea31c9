"""
Diagnostic script to verify dashboard data accuracy and database synchronization
"""

import os
import sys
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_database_connection():
    """Diagnose database connection and data"""
    print("🔍 DIAGNOSING DATABASE CONNECTION")
    print("=" * 50)
    
    try:
        from utils.database_integration import get_database
        
        # Get database instance
        db = get_database()
        print(f"✅ Database connected")
        print(f"📁 Database path: {db.db_path}")
        print(f"🆔 Session ID: {db.session_id}")
        
        # Check if database file exists
        if os.path.exists(db.db_path):
            file_size = os.path.getsize(db.db_path)
            print(f"📊 Database file size: {file_size:,} bytes")
        else:
            print("❌ Database file does not exist!")
            return False
        
        # Test each detection type
        detection_types = ['age', 'object', 'expression', 'anomaly']
        total_records = 0
        
        for det_type in detection_types:
            try:
                records = db.get_recent_detections(det_type, limit=10000)
                count = len(records)
                total_records += count
                print(f"📊 {det_type.upper()}: {count} records")
                
                # Show sample record if available
                if records:
                    sample = records[0]
                    timestamp = sample.get('timestamp', 'No timestamp')
                    confidence = sample.get('confidence', 'No confidence')
                    print(f"   Sample: {timestamp}, confidence: {confidence}")
                    
                    # Check for human vs person in object records
                    if det_type == 'object':
                        human_count = sum(1 for r in records if r.get('object_name') == 'human')
                        person_count = sum(1 for r in records if r.get('object_name') == 'person')
                        print(f"   Human detections: {human_count}")
                        print(f"   Person detections: {person_count}")
                        if person_count > 0:
                            print("   ⚠️ WARNING: Found 'person' instead of 'human'")
                
            except Exception as e:
                print(f"❌ Error getting {det_type} records: {e}")
        
        print(f"📊 TOTAL RECORDS: {total_records}")
        return total_records > 0
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def diagnose_dashboard_connection():
    """Diagnose dashboard database connection"""
    print("\n🎯 DIAGNOSING DASHBOARD CONNECTION")
    print("=" * 50)
    
    try:
        from gui.enhanced_dashboard import StreamlinedDashboard
        
        # Create dashboard instance
        dashboard = StreamlinedDashboard()
        
        if dashboard.enhanced_db:
            print("✅ Dashboard connected to database")
            print(f"📁 Dashboard DB path: {dashboard.enhanced_db.db_path}")
            print(f"🆔 Dashboard session: {dashboard.enhanced_db.session_id}")
            
            # Test dashboard data retrieval
            counts = dashboard.get_current_detection_counts()
            print(f"📊 Dashboard sees: {counts}")
            
            # Test refresh
            print("🔄 Testing dashboard refresh...")
            dashboard.refresh_data()
            
            # Check current data
            total_dashboard = sum(len(data) for data in dashboard.current_data.values())
            print(f"📊 Dashboard loaded: {total_dashboard} records")
            
            for det_type, data in dashboard.current_data.items():
                print(f"   {det_type.upper()}: {len(data)} records")
            
            return True
        else:
            print("❌ Dashboard not connected to database")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard connection failed: {e}")
        return False

def test_real_time_sync():
    """Test real-time synchronization"""
    print("\n⏱️ TESTING REAL-TIME SYNCHRONIZATION")
    print("=" * 50)
    
    try:
        from utils.database_integration import get_database
        from gui.enhanced_dashboard import StreamlinedDashboard
        
        # Get database and dashboard
        db = get_database()
        dashboard = StreamlinedDashboard()
        
        if not dashboard.enhanced_db:
            print("❌ Dashboard not connected - cannot test sync")
            return False
        
        # Get initial counts
        initial_counts = dashboard.get_current_detection_counts()
        print(f"📊 Initial counts: {initial_counts}")
        
        # Add new detection data
        print("➕ Adding new detection data...")
        
        # Add age detection
        db.log_age_detection(
            age=35,
            confidence=0.88,
            model_used="Best_CNN",
            face_bbox="(120, 60, 85, 125)",
            processing_time=0.04
        )

        # Add object detection
        db.log_object_detection(
            object_name="human",  # Using "human" not "person"
            confidence=0.82,
            bbox=(160, 80, 95, 145),
            detection_method="YOLOv8",
            is_anomaly=False
        )
        
        print("✅ New data added to database")
        
        # Refresh dashboard
        print("🔄 Refreshing dashboard...")
        dashboard.refresh_data()
        
        # Get new counts
        new_counts = dashboard.get_current_detection_counts()
        print(f"📊 New counts: {new_counts}")
        
        # Check if counts increased
        age_increased = new_counts.get('age', 0) > initial_counts.get('age', 0)
        object_increased = new_counts.get('object', 0) > initial_counts.get('object', 0)
        
        print(f"📈 Age count increased: {age_increased}")
        print(f"📈 Object count increased: {object_increased}")
        
        if age_increased and object_increased:
            print("✅ Real-time sync working correctly!")
            return True
        else:
            print("⚠️ Real-time sync may have issues")
            return False
            
    except Exception as e:
        print(f"❌ Real-time sync test failed: {e}")
        return False

def check_pdf_version_removal():
    """Check if PDF generation removes version info"""
    print("\n📄 CHECKING PDF VERSION REMOVAL")
    print("=" * 50)
    
    try:
        # Check if ReportLab is available
        try:
            from reportlab.lib.pagesizes import letter
            print("✅ ReportLab available")
        except ImportError:
            print("⚠️ ReportLab not available - cannot test PDF")
            return False
        
        # Check the PDF generation code for version strings
        import inspect
        from gui.enhanced_dashboard import StreamlinedDashboard
        
        # Get the PDF generation method source
        pdf_method = StreamlinedDashboard.generate_pdf_report
        source = inspect.getsource(pdf_method)
        
        # Check for version strings
        version_strings = [
            'v2.0', 'version', 'Version', 'VERSION',
            'Enhanced Dashboard v', 'Report Version'
        ]
        
        found_versions = []
        for version_str in version_strings:
            if version_str in source:
                found_versions.append(version_str)
        
        if found_versions:
            print(f"⚠️ Found version strings in PDF code: {found_versions}")
            return False
        else:
            print("✅ No version strings found in PDF generation code")
            return True
            
    except Exception as e:
        print(f"❌ PDF version check failed: {e}")
        return False

def run_comprehensive_diagnosis():
    """Run comprehensive diagnosis"""
    print("🔧 COMPREHENSIVE DASHBOARD DIAGNOSIS")
    print("=" * 70)
    
    # Run all diagnostic tests
    db_test = diagnose_database_connection()
    dashboard_test = diagnose_dashboard_connection()
    sync_test = test_real_time_sync()
    pdf_test = check_pdf_version_removal()
    
    print("\n📋 DIAGNOSIS RESULTS:")
    print("=" * 30)
    print(f"Database Connection: {'✅ PASS' if db_test else '❌ FAIL'}")
    print(f"Dashboard Connection: {'✅ PASS' if dashboard_test else '❌ FAIL'}")
    print(f"Real-time Sync: {'✅ PASS' if sync_test else '❌ FAIL'}")
    print(f"PDF Version Removal: {'✅ PASS' if pdf_test else '❌ FAIL'}")
    
    overall_pass = all([db_test, dashboard_test, sync_test, pdf_test])
    
    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_pass else '❌ SOME TESTS FAILED'}")
    
    if overall_pass:
        print("\n🎉 Dashboard fixes are working correctly!")
        print("✅ Data accuracy: Dashboard shows real-time data")
        print("✅ PDF reports: No version information included")
        print("✅ Synchronization: Live updates working")
        print("✅ Human labeling: Consistent 'human' usage")
    else:
        print("\n⚠️ Issues found - check individual test results above")
    
    return overall_pass

if __name__ == "__main__":
    print("🔧 DASHBOARD DATA ACCURACY DIAGNOSIS")
    print("=" * 70)
    print("This script diagnoses dashboard data accuracy and synchronization issues")
    print("=" * 70)
    
    result = run_comprehensive_diagnosis()
    
    if result:
        print("\n🚀 Ready to test dashboard with main application!")
        print("1. Run main.py")
        print("2. Start detection (age, object, expression, anomaly)")
        print("3. Open dashboard and verify real-time statistics")
        print("4. Generate PDF report and verify no version info")
    else:
        print("\n🔧 Fix the issues above before testing with main application")
    
    print("\n🔧 Diagnosis completed!")
