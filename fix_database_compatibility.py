"""
Database Compatibility Fix for Existing Database Structure
Updates the unified system to work with your current database schema
"""

import sqlite3
import os
from datetime import datetime

def analyze_current_database():
    """Analyze the current database structure"""
    print("🔍 ANALYZING CURRENT DATABASE STRUCTURE")
    print("=" * 50)
    
    db_path = "detection_results.db"
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 Found {len(tables)} tables:")
        for table in tables:
            table_name = table[0]
            print(f"  📋 {table_name}")
            
            # Get column info for each table
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                print(f"    - {col_name} ({col_type})")
            
            # Get record count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"    📊 Records: {count}")
            print()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        return False

def add_missing_columns():
    """Add missing columns to match unified system requirements"""
    print("🔧 ADDING MISSING COLUMNS")
    print("=" * 30)
    
    db_path = "detection_results.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check and add missing columns to anomaly_detections
        print("🔧 Updating anomaly_detections table...")
        cursor.execute("PRAGMA table_info(anomaly_detections)")
        columns = [col[1] for col in cursor.fetchall()]
        
        missing_anomaly_columns = [
            ('description', 'TEXT DEFAULT ""'),
            ('recording_path', 'TEXT DEFAULT ""'),
            ('report_path', 'TEXT DEFAULT ""'),
            ('objects_detected', 'TEXT DEFAULT ""')
        ]
        
        for col_name, col_def in missing_anomaly_columns:
            if col_name not in columns:
                print(f"  ➕ Adding {col_name} column...")
                cursor.execute(f"ALTER TABLE anomaly_detections ADD COLUMN {col_name} {col_def}")
                print(f"  ✅ Added {col_name}")
        
        # Check and add missing columns to expression_detections
        print("🔧 Updating expression_detections table...")
        cursor.execute("PRAGMA table_info(expression_detections)")
        columns = [col[1] for col in cursor.fetchall()]
        
        missing_expression_columns = [
            ('face_bbox', 'TEXT DEFAULT ""'),
            ('all_probabilities', 'TEXT DEFAULT ""')
        ]
        
        for col_name, col_def in missing_expression_columns:
            if col_name not in columns:
                print(f"  ➕ Adding {col_name} column...")
                cursor.execute(f"ALTER TABLE expression_detections ADD COLUMN {col_name} {col_def}")
                print(f"  ✅ Added {col_name}")
        
        conn.commit()
        conn.close()
        
        print("✅ Database structure updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")
        return False

def test_database_compatibility():
    """Test if the database is now compatible with the unified system"""
    print("🧪 TESTING DATABASE COMPATIBILITY")
    print("=" * 35)
    
    try:
        # Import and test the unified database system
        from utils.database_integration import get_database
        
        db = get_database()
        print(f"✅ Unified database connected: {db.db_path}")
        print(f"🆔 Session ID: {db.session_id}")
        
        # Test each detection type
        test_results = {}
        
        # Test age detection
        print("\n📊 Testing age detection...")
        age_success = db.log_age_detection(
            age=25,
            age_range="Young Adult (20-34)",
            confidence=0.85,
            model_used="Caffe Age Model",
            face_bbox="(100,100,50,70)",
            processing_time=0.12
        )
        test_results['age'] = age_success
        print(f"Age detection: {'✅' if age_success else '❌'}")
        
        # Test object detection
        print("\n🔍 Testing object detection...")
        object_success = db.log_object_detection(
            object_name="human",
            confidence=0.92,
            bbox=(150, 100, 80, 120),
            detection_method="YOLOv8",
            is_anomaly=False
        )
        test_results['object'] = object_success
        print(f"Object detection: {'✅' if object_success else '❌'}")
        
        # Test expression detection
        print("\n🎭 Testing expression detection...")
        expression_success = db.log_expression_detection(
            expression="Happy",
            confidence=0.88,
            model_used="emotion_detection_83.6_percent.pt",
            coordinates="(120,90,60,80)",
            face_bbox="(120,90,60,80)",
            all_probabilities={"Happy": 0.88, "Neutral": 0.08, "Sad": 0.04},
            processing_time=0.22
        )
        test_results['expression'] = expression_success
        print(f"Expression detection: {'✅' if expression_success else '❌'}")
        
        # Test anomaly detection
        print("\n⚠️ Testing anomaly detection...")
        anomaly_success = db.log_anomaly_detection(
            anomaly_type="Suspicious Behavior",
            confidence=0.75,
            threat_level="MEDIUM",
            description="Unusual movement pattern detected in restricted area",
            recording_path="recordings/anomaly_20250619_014500.mp4",
            report_path="reports/anomaly_report_20250619_014500.pdf",
            objects_detected=["human", "bag"]
        )
        test_results['anomaly'] = anomaly_success
        print(f"Anomaly detection: {'✅' if anomaly_success else '❌'}")
        
        # Test data retrieval
        print("\n📈 Testing data retrieval...")
        for det_type in ['age', 'object', 'expression', 'anomaly']:
            data = db.get_recent_detections(det_type, limit=5)
            count = len(data)
            print(f"  {det_type.upper()}: {count} records retrieved")
            if count > 0:
                latest = data[0]
                print(f"    Latest: {latest.get('timestamp', 'N/A')} - Confidence: {latest.get('confidence', 0):.2f}")
        
        # Summary
        successful_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        print(f"\n📊 COMPATIBILITY TEST RESULTS:")
        print(f"✅ Successful: {successful_tests}/{total_tests} detection types")
        
        if successful_tests == total_tests:
            print("🎉 DATABASE IS FULLY COMPATIBLE!")
            print("✅ Your dashboard should now show accurate data!")
            return True
        else:
            print("⚠️ Some compatibility issues remain")
            return False
            
    except Exception as e:
        print(f"❌ Compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to fix database compatibility"""
    print("🔧 DATABASE COMPATIBILITY FIX")
    print("=" * 60)
    print("This script will update your existing database to work")
    print("with the unified detection system.\n")
    
    # Step 1: Analyze current database
    if not analyze_current_database():
        print("❌ Failed to analyze database")
        return False
    
    # Step 2: Add missing columns
    if not add_missing_columns():
        print("❌ Failed to update database structure")
        return False
    
    # Step 3: Test compatibility
    if not test_database_compatibility():
        print("❌ Compatibility test failed")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 DATABASE COMPATIBILITY FIX COMPLETED!")
    print("✅ Your database is now fully compatible with the unified system")
    print("✅ Dashboard data accuracy issues should be resolved")
    print("✅ All detection types should now log correctly")
    
    return True

if __name__ == "__main__":
    main()
