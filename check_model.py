#!/usr/bin/env python3
"""
Quick Diagnosis Script - Run this to find out exactly why your model isn't loading
Save this as check_model.py and run: python check_model.py
"""

import os
import sys

def quick_check():
    print("🔍 QUICK YOLOV8 MODEL DIAGNOSIS")
    print("=" * 50)
    
    # 1. Check if model file exists
    print("\n1️⃣ Checking model file...")
    
    possible_locations = [
        "emotion_detection_83.6_percent.pt",
        "models/emotion_detection_83.6_percent.pt",
        "./emotion_detection_83.6_percent.pt", 
        "./models/emotion_detection_83.6_percent.pt"
    ]
    
    model_found = False
    for location in possible_locations:
        if os.path.exists(location):
            print(f"✅ FOUND: {location}")
            size_mb = os.path.getsize(location) / (1024 * 1024)
            print(f"   Size: {size_mb:.1f} MB")
            model_found = True
            model_path = location
            break
        else:
            print(f"❌ NOT FOUND: {location}")
    
    if not model_found:
        print("\n🚨 PROBLEM FOUND: Model file not found!")
        print("🔧 SOLUTION: Place your model file in one of these locations:")
        print("   - emotion_detection_83.6_percent.pt (root directory)")
        print("   - models/emotion_detection_83.6_percent.pt")
        return
    
    # 2. Check ultralytics
    print("\n2️⃣ Checking ultralytics installation...")
    try:
        import ultralytics
        from ultralytics import YOLO
        print(f"✅ Ultralytics installed: {ultralytics.__version__}")
    except ImportError:
        print("❌ Ultralytics not installed!")
        print("🔧 SOLUTION: pip install ultralytics")
        return
    
    # 3. Test model loading
    print("\n3️⃣ Testing model loading...")
    try:
        model = YOLO(model_path)
        print("✅ Model loaded successfully!")
        print(f"📋 Classes: {model.names}")
        
        # Test prediction
        import numpy as np
        dummy = np.zeros((640, 640, 3), dtype=np.uint8)
        results = model.predict(dummy, verbose=False, conf=0.1)
        print("✅ Model prediction test passed!")
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        print("🔧 Possible solutions:")
        print("   - Re-download your model file")
        print("   - Update ultralytics: pip install --upgrade ultralytics")
        print("   - Check if model file is corrupted")
        return
    
    # 4. Check integration file
    print("\n4️⃣ Checking integration file...")
    
    integration_files = [
        "detection/custom_yolo_expression.py",
        "detection/enhanced_expression_popup.py"
    ]
    
    for file_path in integration_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            print(f"🔧 Create this file with the provided code")
    
    # 5. Test integration
    print("\n5️⃣ Testing integration...")
    try:
        sys.path.append('.')
        from detection.custom_yolo_expression import CustomYOLOv8ExpressionDetector
        
        detector = CustomYOLOv8ExpressionDetector()
        
        if detector.is_model_loaded():
            print("✅ Integration successful!")
            print("🎉 Your model should work now!")
        else:
            print("❌ Integration failed - model not loaded in detector")
            
    except ImportError as e:
        print(f"❌ Cannot import detector: {e}")
        print("🔧 Make sure detection/custom_yolo_expression.py exists")
    except Exception as e:
        print(f"❌ Integration error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Diagnosis complete!")

if __name__ == "__main__":
    quick_check()