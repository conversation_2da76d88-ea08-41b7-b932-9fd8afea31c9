#!/usr/bin/env python3
"""
Apply quick fixes to existing code
"""

import os
import shutil

def fix_config_file():
    """Fix the config.py file to include missing attributes"""
    config_content = '''"""
Configuration settings for AI Video Detection Tools
"""

import os
from dataclasses import dataclass
from typing import List, Tuple

@dataclass
class Config:
    """Application configuration settings"""
    
    # Directories
    BASE_DIR: str = "Security_Footage"
    MODELS_DIR: str = "models"
    LOGS_DIR: str = "logs"
    
    # Recording settings
    MAX_FOLDERS: int = 10
    RECORDING_DURATION: int = 10  # seconds
    FPS: int = 30
    
    # Detection settings
    CONFIDENCE_THRESHOLD: float = 0.5
    NMS_THRESHOLD: float = 0.4
    YOLO_INPUT_SIZE: Tuple[int, int] = (416, 416)
    
    # Facial expression settings
    FACE_SIZE: Tuple[int, int] = (48, 48)
    EXPRESSION_LABELS: dict = None
    
    # Anomaly objects to detect
    ANOMALIES: List[str] = None
    
    # Authentication
    DEFAULT_USERNAME: str = "admin"
    DEFAULT_PASSWORD: str = "password123"
    
    # GUI settings
    WINDOW_SIZE: Tuple[int, int] = (1200, 800)
    VIDEO_DISPLAY_SIZE: Tuple[int, int] = (640, 480)
    
    # Colors (BGR format for OpenCV)
    COLOR_GREEN: Tuple[int, int, int] = (0, 255, 0)
    COLOR_RED: Tuple[int, int, int] = (0, 0, 255)
    COLOR_YELLOW: Tuple[int, int, int] = (0, 255, 255)
    COLOR_BLUE: Tuple[int, int, int] = (255, 0, 0)
    
    def __post_init__(self):
        """Initialize configuration after object creation"""
        self.EXPRESSION_LABELS = {
            0: "Angry",
            1: "Disgust", 
            2: "Fear",
            3: "Happy",
            4: "Sad",
            5: "Surprise",
            6: "Neutral"
        }
        
        self.ANOMALIES = ["gun", "knife", "scissors", "weapon"]
        
        # Create directories if they don't exist
        for directory in [self.BASE_DIR, self.MODELS_DIR, self.LOGS_DIR]:
            os.makedirs(directory, exist_ok=True)
    
    def get_model_path(self, model_name: str) -> str:
        """Get full path to model file"""
        return os.path.join(self.MODELS_DIR, model_name)
    
    def get_log_path(self, log_name: str) -> str:
        """Get full path to log file"""
        return os.path.join(self.LOGS_DIR, log_name)
'''
    
    with open("utils/config.py", "w") as f:
        f.write(config_content)
    print("✅ Fixed utils/config.py")

def fix_facial_expression():
    """Add safe TensorFlow model loading"""
    # Read the current file
    try:
        with open("detection/facial_expression.py", "r") as f:
            content = f.read()
        
        # Replace problematic load_model calls
        content = content.replace(
            "self.cnn_model = keras.models.load_model(cnn_path)",
            "self.cnn_model = keras.models.load_model(cnn_path, compile=False)"
        )
        content = content.replace(
            "self.best_cnn_model = keras.models.load_model(best_cnn_path)",
            "self.best_cnn_model = keras.models.load_model(best_cnn_path, compile=False)"
        )
        
        # Write back
        with open("detection/facial_expression.py", "w") as f:
            f.write(content)
        print("✅ Fixed detection/facial_expression.py")
        
    except FileNotFoundError:
        print("❌ detection/facial_expression.py not found")

def fix_login_credentials():
    """Fix login credential checking"""
    try:
        with open("gui/login_window.py", "r") as f:
            content = f.read()
        
        # Make sure credentials are case-insensitive and trimmed
        old_check = 'if (username == self.config.DEFAULT_USERNAME and \n            password == self.config.DEFAULT_PASSWORD):'
        new_check = 'if (username.lower().strip() == self.config.DEFAULT_USERNAME.lower() and \n            password.strip() == self.config.DEFAULT_PASSWORD):'
        
        content = content.replace(old_check, new_check)
        
        with open("gui/login_window.py", "w") as f:
            f.write(content)
        print("✅ Fixed login credentials checking")
        
    except FileNotFoundError:
        print("❌ gui/login_window.py not found")

def create_dummy_models():
    """Create dummy model files to prevent loading errors"""
    models_dir = "models"
    os.makedirs(models_dir, exist_ok=True)
    
    # Create dummy files if they don't exist
    dummy_files = {
        "coco.names": "person\nbicycle\ncar\nmotorbike\naeroplane\nbus\ntrain\ntruck\nboat\ntraffic light\nfire hydrant\nstop sign\nparking meter\nbench\nbird\ncat\ndog\nhorse\nsheep\ncow\nelephant\nbear\nzebra\ngiraffe\nbackpack\numbrella\nhandbag\ntie\nsuitcase\nfrisbee\nskis\nsnowboard\nsports ball\nkite\nbaseball bat\nbaseball glove\nskateboard\nsurfboard\ntennis racket\nbottle\nwine glass\ncup\nfork\nknife\nspoon\nbowl\nbanana\napple\nsandwich\norange\nbroccoli\ncarrot\nhot dog\npizza\ndonut\ncake\nchair\nsofa\npottedplant\nbed\ndiningtable\ntoilet\ntvmonitor\nlaptop\nmouse\nremote\nkeyboard\ncell phone\nmicrowave\noven\ntoaster\nsink\nrefrigerator\nbook\nclock\nvase\nscissors\nteddy bear\nhair drier\ntoothbrush",
        "yolov3.cfg": "[net]\n# Testing\nbatch=1\nsubdivisions=1\n# Training\n# batch=64\n# subdivisions=16\nwidth=416\nheight=416\nchannels=3\nmomentum=0.9\ndecay=0.0005\nangle=0\nsaturation = 1.5\nexposure = 1.5\nhue=.1\n\nlearning_rate=0.001\nburn_in=1000\nmax_batches = 500200\npolicy=steps\nsteps=400000,450000\nscales=.1,.1"
    }
    
    for filename, content in dummy_files.items():
        filepath = os.path.join(models_dir, filename)
        if not os.path.exists(filepath):
            with open(filepath, "w") as f:
                f.write(content)
            print(f"✅ Created dummy {filename}")

def main():
    """Apply all fixes"""
    print("🔧 Applying fixes to existing code...")
    print("=" * 40)
    
    # Apply fixes
    fix_config_file()
    fix_facial_expression()
    fix_login_credentials()
    create_dummy_models()
    
    print("\n🎉 Fixes applied!")
    print("\nNext steps:")
    print("1. Run: python download_models.py (to get real AI models)")
    print("2. Run: python main.py")
    print("3. Login with: admin / password123")
    
    print("\nNote: The app will work with basic detection even without full AI models.")

if __name__ == "__main__":
    main()