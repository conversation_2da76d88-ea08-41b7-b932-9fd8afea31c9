# Test script to verify installations
try:
    import ultralytics
    print("✅ Ultralytics YOLOv8 installed successfully")
    
    import torch
    print(f"✅ PyTorch installed successfully - Version: {torch.__version__}")
    print(f"   CUDA available: {torch.cuda.is_available()}")
    
    import tensorflow as tf
    print(f"✅ TensorFlow installed successfully - Version: {tf.__version__}")
    
    import cv2
    print(f"✅ OpenCV installed successfully - Version: {cv2.__version__}")
    
    print("\n🎉 All packages installed correctly!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")