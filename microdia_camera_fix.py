#!/usr/bin/env python3
"""
Microdia Camera Color Fix
Specific fix for Microdia webcams (VID_0C45) grayscale issues
"""

import cv2
import numpy as np
import subprocess
import time

def microdia_specific_fix():
    """Apply Microdia-specific color fixes"""
    print("🎥 Microdia Camera Color Fix Tool")
    print("=" * 50)
    print("📷 Detected: Microdia Webcam (VID_0C45&PID_6725)")
    print("🔧 Applying Microdia-specific color fixes...")
    
    # Try different approaches for Microdia cameras
    approaches = [
        ("DirectShow with UVC controls", fix_with_directshow_uvc),
        ("Media Foundation with format override", fix_with_msmf_format),
        ("Raw USB control", fix_with_usb_control),
        ("V4L2 compatibility mode", fix_with_v4l2_compat)
    ]
    
    for approach_name, fix_function in approaches:
        print(f"\n🔄 Trying: {approach_name}")
        try:
            success = fix_function()
            if success:
                print(f"✅ SUCCESS with {approach_name}!")
                return True
            else:
                print(f"❌ {approach_name} failed")
        except Exception as e:
            print(f"❌ {approach_name} error: {e}")
    
    print("\n❌ All software approaches failed")
    return False

def fix_with_directshow_uvc():
    """Fix using DirectShow with UVC controls"""
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        if not cap.isOpened():
            return False
        
        print("📷 Opened with DirectShow backend")
        
        # Microdia-specific UVC controls
        uvc_controls = [
            # Standard UVC controls
            (cv2.CAP_PROP_BRIGHTNESS, 128, "Brightness"),
            (cv2.CAP_PROP_CONTRAST, 128, "Contrast"),
            (cv2.CAP_PROP_SATURATION, 128, "Saturation"),
            (cv2.CAP_PROP_HUE, 0, "Hue"),
            (cv2.CAP_PROP_GAIN, 64, "Gain"),
            (cv2.CAP_PROP_EXPOSURE, -6, "Exposure"),
            
            # Color-specific controls
            (cv2.CAP_PROP_AUTO_WB, 1, "Auto White Balance"),
            (cv2.CAP_PROP_WB_TEMPERATURE, 4000, "White Balance Temperature"),
            (cv2.CAP_PROP_GAMMA, 100, "Gamma"),
            
            # Format controls
            (cv2.CAP_PROP_CONVERT_RGB, 1, "Convert to RGB"),
            (cv2.CAP_PROP_MONOCHROME, 0, "Disable Monochrome"),
        ]
        
        fixes_applied = 0
        for prop, value, name in uvc_controls:
            try:
                if cap.set(prop, value):
                    print(f"✅ {name}: {value}")
                    fixes_applied += 1
                else:
                    print(f"⚠️ {name}: Not supported")
            except:
                print(f"❌ {name}: Failed")
        
        # Force YUYV format (common for Microdia)
        try:
            fourcc = cv2.VideoWriter_fourcc('Y', 'U', 'Y', 'V')
            cap.set(cv2.CAP_PROP_FOURCC, fourcc)
            print("✅ Set YUYV pixel format")
        except:
            print("⚠️ YUYV format not supported")
        
        # Test result
        ret, frame = cap.read()
        if ret:
            color_score = analyze_color_quality(frame)
            print(f"📊 Color score: {color_score:.2f}")
            
            if color_score > 5.0:
                print("🎉 Color mode activated!")
                show_preview(cap, "DirectShow UVC Fix")
                cap.release()
                return True
        
        cap.release()
        return False
        
    except Exception as e:
        print(f"❌ DirectShow UVC error: {e}")
        return False

def fix_with_msmf_format():
    """Fix using Media Foundation with format override"""
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_MSMF)
        if not cap.isOpened():
            return False
        
        print("📷 Opened with Media Foundation backend")
        
        # Set specific resolution and format for Microdia
        resolutions = [(640, 480), (1280, 720), (320, 240)]
        
        for width, height in resolutions:
            print(f"🔧 Trying resolution: {width}x{height}")
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            
            # Force color format
            cap.set(cv2.CAP_PROP_CONVERT_RGB, 1)
            cap.set(cv2.CAP_PROP_SATURATION, 255)  # Maximum saturation
            
            ret, frame = cap.read()
            if ret:
                color_score = analyze_color_quality(frame)
                print(f"📊 Color score at {width}x{height}: {color_score:.2f}")
                
                if color_score > 5.0:
                    print(f"✅ Color mode at {width}x{height}!")
                    show_preview(cap, f"MSMF {width}x{height}")
                    cap.release()
                    return True
        
        cap.release()
        return False
        
    except Exception as e:
        print(f"❌ MSMF error: {e}")
        return False

def fix_with_usb_control():
    """Try USB-level control (requires admin)"""
    try:
        print("🔧 Attempting USB-level control...")
        
        # This would require libusb or similar
        # For now, just suggest manual USB reset
        print("💡 Manual USB reset suggested:")
        print("1. Unplug camera USB cable")
        print("2. Wait 10 seconds")
        print("3. Plug back in")
        print("4. Test camera again")
        
        return False
        
    except Exception as e:
        print(f"❌ USB control error: {e}")
        return False

def fix_with_v4l2_compat():
    """Try V4L2 compatibility mode"""
    try:
        # This is mainly for Linux, but some Windows drivers support it
        cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
        if cap.isOpened():
            print("📷 V4L2 backend available")
            
            # V4L2 color controls
            cap.set(cv2.CAP_PROP_SATURATION, 200)
            cap.set(cv2.CAP_PROP_CONTRAST, 150)
            
            ret, frame = cap.read()
            if ret:
                color_score = analyze_color_quality(frame)
                if color_score > 5.0:
                    show_preview(cap, "V4L2 Compatibility")
                    cap.release()
                    return True
            
            cap.release()
        
        return False
        
    except Exception as e:
        print(f"❌ V4L2 error: {e}")
        return False

def analyze_color_quality(frame):
    """Analyze frame for color quality"""
    if len(frame.shape) != 3:
        return 0.0
        
    b, g, r = cv2.split(frame)
    
    # Calculate channel differences
    bg_diff = np.abs(b.astype(float) - g.astype(float)).mean()
    gr_diff = np.abs(g.astype(float) - r.astype(float)).mean()
    br_diff = np.abs(b.astype(float) - r.astype(float)).mean()
    
    return bg_diff + gr_diff + br_diff

def show_preview(cap, method_name):
    """Show camera preview"""
    print(f"📺 Showing preview with {method_name} (Press ESC to close)")
    
    for i in range(150):  # 5 seconds
        ret, frame = cap.read()
        if ret:
            cv2.imshow(f'Color Preview - {method_name}', frame)
            if cv2.waitKey(1) & 0xFF == 27:  # ESC
                break
    
    cv2.destroyAllWindows()

def suggest_microdia_solutions():
    """Suggest Microdia-specific solutions"""
    print("\n" + "=" * 50)
    print("🔧 MICRODIA-SPECIFIC SOLUTIONS:")
    print("=" * 50)
    
    solutions = [
        "1. 📱 Check Windows Camera App:",
        "   - Open Windows Camera app",
        "   - Go to Settings → Camera options",
        "   - Look for 'Color effects' or 'Filters'",
        "   - Disable 'Black and white' or 'Grayscale'",
        "",
        "2. 🔧 Device Manager Advanced Settings:",
        "   - Device Manager → Cameras → Your Microdia camera",
        "   - Properties → Advanced tab",
        "   - Look for 'Color Enable' or 'Monochrome Disable'",
        "   - Set to appropriate values",
        "",
        "3. 🏭 Microdia Driver Update:",
        "   - Visit Microdia support website",
        "   - Download latest drivers for your model",
        "   - Some Microdia cameras need specific drivers",
        "",
        "4. 🎯 Registry Fix (Advanced):",
        "   - Some Microdia cameras store color settings in registry",
        "   - Search for 'Microdia camera registry color fix'",
        "   - Backup registry before making changes",
        "",
        "5. 🔌 Hardware Solutions:",
        "   - Some Microdia cameras have IR mode",
        "   - Check for physical IR/Color switch",
        "   - Try covering IR sensor if present",
        "",
        "6. 🔄 USB Power Cycle:",
        "   - Unplug camera for 30 seconds",
        "   - Plug into different USB port",
        "   - Some Microdia cameras reset to color mode",
    ]
    
    for solution in solutions:
        print(solution)

def main():
    """Main function"""
    print("🎥 Microdia Camera Color Fix")
    print("Specialized tool for Microdia webcams")
    print("=" * 50)
    
    # Try software fixes first
    success = microdia_specific_fix()
    
    if not success:
        suggest_microdia_solutions()
        
        print("\n💡 IMPORTANT NOTES:")
        print("- Microdia cameras often have hardware color/IR modes")
        print("- Some models require specific driver versions")
        print("- Check camera manual for color mode instructions")
        print("- Try testing camera in other applications first")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
