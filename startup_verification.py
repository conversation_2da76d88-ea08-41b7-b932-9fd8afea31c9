#!/usr/bin/env python3
"""
AI Video Detection - Startup Verification Script
Comprehensive verification of all application components
"""

import sys
import os
import importlib
from datetime import datetime

def print_header():
    """Print verification header"""
    print("🛡️ AI VIDEO DETECTION - STARTUP VERIFICATION")
    print("=" * 60)
    print(f"🕒 Verification started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

def verify_dependencies():
    """Verify all required dependencies"""
    print("\n📦 DEPENDENCY VERIFICATION")
    print("-" * 40)
    
    dependencies = [
        ('tkinter', 'GUI framework'),
        ('cv2', 'OpenCV for computer vision'),
        ('PIL', 'Pillow for image processing'),
        ('numpy', 'Numerical computing'),
        ('threading', 'Multi-threading support'),
        ('datetime', 'Date and time utilities'),
        ('logging', 'Logging framework'),
        ('os', 'Operating system interface'),
        ('math', 'Mathematical functions')
    ]
    
    missing_deps = []
    
    for dep_name, description in dependencies:
        try:
            importlib.import_module(dep_name)
            print(f"✅ {dep_name:<12} - {description}")
        except ImportError:
            print(f"❌ {dep_name:<12} - {description} (MISSING)")
            missing_deps.append(dep_name)
    
    # Check optional dependencies
    optional_deps = [
        ('ultralytics', 'YOLOv8 for advanced detection'),
        ('torch', 'PyTorch for deep learning'),
        ('tensorflow', 'TensorFlow for ML models')
    ]
    
    print("\n🔧 OPTIONAL DEPENDENCIES")
    print("-" * 40)
    
    for dep_name, description in optional_deps:
        try:
            importlib.import_module(dep_name)
            print(f"✅ {dep_name:<12} - {description}")
        except ImportError:
            print(f"⚠️ {dep_name:<12} - {description} (Optional)")
    
    return missing_deps

def verify_file_structure():
    """Verify application file structure"""
    print("\n📁 FILE STRUCTURE VERIFICATION")
    print("-" * 40)
    
    required_files = [
        ('main.py', 'Main application entry point'),
        ('gui/main_window.py', 'Main window implementation'),
        ('gui/ios_theme.py', 'iOS theme components'),
        ('gui/login_window.py', 'Login window'),
        ('utils/config.py', 'Configuration settings'),
        ('detection/facial_expression.py', 'Facial expression detection'),
        ('detection/object_detection.py', 'Object detection'),
        ('detection/age_detection.py', 'Age detection')
    ]
    
    missing_files = []
    
    for file_path, description in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path:<30} - {description}")
        else:
            print(f"❌ {file_path:<30} - {description} (MISSING)")
            missing_files.append(file_path)
    
    return missing_files

def verify_imports():
    """Verify all module imports"""
    print("\n🔗 MODULE IMPORT VERIFICATION")
    print("-" * 40)
    
    modules_to_test = [
        ('gui.main_window', 'Main window module'),
        ('gui.ios_theme', 'iOS theme module'),
        ('gui.login_window', 'Login window module'),
        ('utils.config', 'Configuration module'),
        ('detection.facial_expression', 'Facial expression module'),
        ('detection.object_detection', 'Object detection module'),
        ('detection.age_detection', 'Age detection module')
    ]
    
    import_errors = []
    
    for module_name, description in modules_to_test:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name:<30} - {description}")
        except ImportError as e:
            print(f"❌ {module_name:<30} - {description} (ERROR: {e})")
            import_errors.append((module_name, str(e)))
        except Exception as e:
            print(f"⚠️ {module_name:<30} - {description} (WARNING: {e})")
    
    return import_errors

def verify_class_initialization():
    """Verify key class initialization"""
    print("\n🏗️ CLASS INITIALIZATION VERIFICATION")
    print("-" * 40)
    
    try:
        # Test iOS theme components
        from gui.ios_theme import iOSTheme, iOSButton, iOSCard, iOSStatusIndicator
        print("✅ iOS theme classes imported successfully")
        
        # Test config
        from utils.config import Config
        config = Config()
        print("✅ Configuration class initialized successfully")
        
        # Test main window class (without creating GUI)
        from gui.main_window import MainWindow
        print("✅ MainWindow class imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Class initialization failed: {e}")
        return False

def verify_ai_detection_modules():
    """Verify AI detection modules"""
    print("\n🤖 AI DETECTION MODULE VERIFICATION")
    print("-" * 40)
    
    detection_modules = [
        ('detection.facial_expression', 'FacialExpressionDetector'),
        ('detection.object_detection', 'ObjectDetector'),
        ('detection.age_detection', 'AgeDetector')
    ]
    
    working_modules = []
    
    for module_name, class_name in detection_modules:
        try:
            module = importlib.import_module(module_name)
            detector_class = getattr(module, class_name)
            print(f"✅ {class_name:<25} - Class available")
            working_modules.append(class_name)
        except ImportError as e:
            print(f"❌ {class_name:<25} - Import error: {e}")
        except AttributeError as e:
            print(f"❌ {class_name:<25} - Class not found: {e}")
        except Exception as e:
            print(f"⚠️ {class_name:<25} - Warning: {e}")
    
    return working_modules

def verify_directories():
    """Verify required directories exist"""
    print("\n📂 DIRECTORY VERIFICATION")
    print("-" * 40)
    
    required_dirs = [
        'gui',
        'utils', 
        'detection',
        'models',
        'logs'
    ]
    
    missing_dirs = []
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"✅ {dir_name}/ directory exists")
        else:
            print(f"❌ {dir_name}/ directory missing")
            missing_dirs.append(dir_name)
    
    return missing_dirs

def run_startup_test():
    """Run a basic startup test"""
    print("\n🚀 STARTUP TEST")
    print("-" * 40)
    
    try:
        print("🔄 Testing application imports...")
        
        # Import main components
        from gui.main_window import MainWindow
        from gui.ios_theme import iOSTheme
        from utils.config import Config
        
        print("✅ All main components imported successfully")
        print("✅ Application is ready to start!")
        
        return True
        
    except Exception as e:
        print(f"❌ Startup test failed: {e}")
        return False

def main():
    """Run complete verification"""
    print_header()
    
    # Run all verifications
    missing_deps = verify_dependencies()
    missing_files = verify_file_structure()
    import_errors = verify_imports()
    class_init_success = verify_class_initialization()
    working_modules = verify_ai_detection_modules()
    missing_dirs = verify_directories()
    startup_success = run_startup_test()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    total_issues = len(missing_deps) + len(missing_files) + len(import_errors) + len(missing_dirs)
    
    if total_issues == 0 and class_init_success and startup_success:
        print("🎉 ALL VERIFICATIONS PASSED!")
        print("✅ Application is ready to launch")
        print("✅ All dependencies are available")
        print("✅ All files are present")
        print("✅ All modules import successfully")
        print("✅ All classes initialize properly")
        print(f"✅ {len(working_modules)} AI detection modules available")
        
        print("\n🚀 TO START THE APPLICATION:")
        print("   python main.py")
        
    else:
        print("⚠️ ISSUES FOUND:")
        
        if missing_deps:
            print(f"   - {len(missing_deps)} missing dependencies")
        if missing_files:
            print(f"   - {len(missing_files)} missing files")
        if import_errors:
            print(f"   - {len(import_errors)} import errors")
        if missing_dirs:
            print(f"   - {len(missing_dirs)} missing directories")
        if not class_init_success:
            print("   - Class initialization issues")
        if not startup_success:
            print("   - Startup test failed")
        
        print("\n🔧 RECOMMENDED ACTIONS:")
        if missing_deps:
            print("   1. Install missing dependencies with pip")
        if missing_files or missing_dirs:
            print("   2. Ensure all required files are present")
        if import_errors:
            print("   3. Fix import errors in modules")
    
    print(f"\n🕒 Verification completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
