#!/usr/bin/env python3
"""
Camera Registry Check
Check Windows registry for camera color settings
"""

import subprocess
import sys

def check_camera_registry():
    """Check camera settings in Windows registry"""
    print("🔍 Checking Windows Registry for camera settings...")
    print("=" * 50)
    
    # Registry paths to check
    registry_paths = [
        r"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{ca3e7ab9-b4c3-4ae6-8251-579ef933890f}",
        r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Camera",
        r"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Camera",
    ]
    
    for path in registry_paths:
        print(f"\n📂 Checking: {path}")
        try:
            # Use reg query to check registry
            result = subprocess.run(
                ['reg', 'query', path, '/s'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                
                # Look for color-related settings
                color_keywords = ['color', 'grayscale', 'monochrome', 'saturation', 'brightness']
                found_settings = []
                
                for line in output.split('\n'):
                    for keyword in color_keywords:
                        if keyword.lower() in line.lower():
                            found_settings.append(line.strip())
                
                if found_settings:
                    print("✅ Found color-related settings:")
                    for setting in found_settings:
                        print(f"   {setting}")
                else:
                    print("⚠️ No color settings found in this path")
            else:
                print("❌ Path not found or access denied")
                
        except subprocess.TimeoutExpired:
            print("⏰ Registry query timed out")
        except Exception as e:
            print(f"❌ Error: {e}")

def suggest_manual_fixes():
    """Suggest manual fixes"""
    print("\n" + "=" * 50)
    print("🔧 MANUAL FIXES TO TRY:")
    print("=" * 50)
    
    fixes = [
        "1. 📱 Windows Camera App:",
        "   - Open Windows Camera app",
        "   - Click Settings (gear icon)",
        "   - Look for 'Video quality' or 'Camera options'",
        "   - Disable any 'Grayscale' or 'Night mode' options",
        "",
        "2. 🔧 Device Manager:",
        "   - Open Device Manager (devmgmt.msc)",
        "   - Find camera under 'Cameras' or 'Imaging devices'",
        "   - Right-click → Properties → Advanced",
        "   - Look for color/grayscale settings",
        "",
        "3. 🏭 Camera Manufacturer Software:",
        "   - Check if camera has dedicated software",
        "   - Look for brand-specific camera utilities",
        "   - Common brands: Logitech, Microsoft, Creative, etc.",
        "",
        "4. 🔌 Hardware Check:",
        "   - Look for physical switches on camera",
        "   - Check for IR/Night mode toggle",
        "   - Try different USB ports",
        "   - Test camera on another computer",
        "",
        "5. 🔄 Driver Solutions:",
        "   - Uninstall camera driver completely",
        "   - Restart computer",
        "   - Let Windows reinstall driver automatically",
        "   - Or download latest driver from manufacturer",
        "",
        "6. 🎯 Camera Model Specific:",
        "   - Search '[Your Camera Model] grayscale fix'",
        "   - Check manufacturer support pages",
        "   - Look for firmware updates",
    ]
    
    for fix in fixes:
        print(fix)

def check_camera_info():
    """Get camera information"""
    print("\n🔍 Getting camera device information...")
    
    try:
        # Get camera devices using PowerShell
        ps_command = """
        Get-WmiObject -Class Win32_PnPEntity | Where-Object {
            $_.Name -like "*camera*" -or 
            $_.Name -like "*webcam*" -or 
            $_.Name -like "*imaging*"
        } | Select-Object Name, DeviceID, Manufacturer | Format-Table -AutoSize
        """
        
        result = subprocess.run(
            ['powershell', '-Command', ps_command],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            print("📷 Camera devices found:")
            print(result.stdout)
        else:
            print("❌ Could not get camera information")
            
    except Exception as e:
        print(f"❌ Error getting camera info: {e}")

def main():
    """Main function"""
    print("🎥 Camera Registry & Hardware Check Tool")
    print("=" * 50)
    
    # Check if running as admin
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            print("✅ Running with administrator privileges")
        else:
            print("⚠️ Not running as administrator - some checks may be limited")
    except:
        print("⚠️ Cannot determine admin status")
    
    # Get camera information
    check_camera_info()
    
    # Check registry
    check_camera_registry()
    
    # Suggest manual fixes
    suggest_manual_fixes()
    
    print("\n" + "=" * 50)
    print("💡 SUMMARY:")
    print("Your camera is hardware-locked in grayscale mode.")
    print("This requires manual configuration through:")
    print("1. Camera manufacturer software")
    print("2. Windows Camera app settings")
    print("3. Device Manager properties")
    print("4. Physical camera switches/buttons")
    print("=" * 50)

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
