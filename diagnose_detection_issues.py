"""
Comprehensive Diagnostic Script for Object Detection and Anomaly Detection Issues
This script will help identify and fix problems with your detection systems
"""

import os
import sys
import cv2
import numpy as np
from datetime import datetime

def check_model_files():
    """Check if YOLO model files exist and are accessible"""
    print("🔍 Checking YOLO Model Files...")
    print("=" * 50)
    
    model_files = {
        "yolov3.weights": "models/yolov3.weights",
        "yolov3.cfg": "models/yolov3.cfg", 
        "coco.names": "models/coco.names"
    }
    
    all_files_exist = True
    
    for name, path in model_files.items():
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✅ {name}: Found ({size:,} bytes)")
            
            # Check file size expectations
            if name == "yolov3.weights" and size < 200_000_000:  # ~248MB expected
                print(f"   ⚠️ WARNING: {name} seems too small (expected ~248MB)")
            elif name == "yolov3.cfg" and size < 1000:  # Should be a few KB
                print(f"   ⚠️ WARNING: {name} seems too small")
            elif name == "coco.names" and size < 500:  # Should be ~1KB
                print(f"   ⚠️ WARNING: {name} seems too small")
        else:
            print(f"❌ {name}: NOT FOUND at {path}")
            all_files_exist = False
    
    return all_files_exist

def test_opencv_dnn():
    """Test OpenCV DNN functionality"""
    print("\n🧪 Testing OpenCV DNN...")
    print("=" * 50)
    
    try:
        # Test basic OpenCV DNN
        net = cv2.dnn.readNet("models/yolov3.weights", "models/yolov3.cfg")
        print("✅ OpenCV can load YOLO model")
        
        # Test layer names
        layer_names = net.getLayerNames()
        unconnected_layers = net.getUnconnectedOutLayers()
        print(f"✅ Model has {len(layer_names)} layers")
        print(f"✅ Output layers: {len(unconnected_layers)}")
        
        # Test with dummy input
        dummy_frame = np.zeros((416, 416, 3), dtype=np.uint8)
        blob = cv2.dnn.blobFromImage(dummy_frame, 1/255.0, (416, 416), (0, 0, 0), True, crop=False)
        net.setInput(blob)
        
        # Get output layer names
        if len(unconnected_layers.shape) == 1:
            output_layers = [layer_names[i - 1] for i in unconnected_layers]
        else:
            output_layers = [layer_names[i[0] - 1] for i in unconnected_layers]
        
        print(f"✅ Output layer names: {output_layers}")
        
        # Test forward pass
        outputs = net.forward(output_layers)
        print(f"✅ Forward pass successful: {len(outputs)} outputs")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV DNN test failed: {e}")
        return False

def test_class_names():
    """Test loading and parsing class names"""
    print("\n📝 Testing Class Names...")
    print("=" * 50)
    
    try:
        with open("models/coco.names", 'r') as f:
            class_names = [line.strip() for line in f.readlines()]
        
        print(f"✅ Loaded {len(class_names)} class names")
        print("📋 First 10 classes:")
        for i, name in enumerate(class_names[:10]):
            print(f"   {i}: {name}")
        
        # Check for expected classes (note: 'person' will be changed to 'human' in detection)
        expected_classes = ['person', 'bicycle', 'car', 'motorcycle', 'airplane']
        for cls in expected_classes:
            if cls in class_names:
                print(f"✅ Found expected class: {cls}")
                if cls == 'person':
                    print(f"   💡 Note: 'person' will be displayed as 'human' in detection")
            else:
                print(f"❌ Missing expected class: {cls}")
        
        return True
        
    except Exception as e:
        print(f"❌ Class names test failed: {e}")
        return False

def test_detection_pipeline():
    """Test the complete detection pipeline"""
    print("\n🔬 Testing Detection Pipeline...")
    print("=" * 50)
    
    try:
        # Load model
        net = cv2.dnn.readNet("models/yolov3.weights", "models/yolov3.cfg")
        
        # Load class names
        with open("models/coco.names", 'r') as f:
            class_names = [line.strip() for line in f.readlines()]
        
        # Create test image with some content
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Add some shapes to detect
        cv2.rectangle(test_frame, (100, 100), (200, 200), (255, 255, 255), -1)
        cv2.circle(test_frame, (400, 300), 50, (128, 128, 128), -1)
        cv2.putText(test_frame, "TEST", (300, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        print("✅ Created test frame")
        
        # Prepare input
        height, width = test_frame.shape[:2]
        blob = cv2.dnn.blobFromImage(test_frame, 1/255.0, (416, 416), (0, 0, 0), True, crop=False)
        
        # Run detection
        net.setInput(blob)
        layer_names = net.getLayerNames()
        unconnected_layers = net.getUnconnectedOutLayers()
        
        if len(unconnected_layers.shape) == 1:
            output_layers = [layer_names[i - 1] for i in unconnected_layers]
        else:
            output_layers = [layer_names[i[0] - 1] for i in unconnected_layers]
        
        outputs = net.forward(output_layers)
        print(f"✅ Got {len(outputs)} output tensors")
        
        # Parse detections
        detections = []
        for output in outputs:
            for detection in output:
                scores = detection[5:]
                class_id = np.argmax(scores)
                confidence = scores[class_id]
                
                if confidence > 0.1:  # Very low threshold for testing
                    center_x = int(detection[0] * width)
                    center_y = int(detection[1] * height)
                    w = int(detection[2] * width)
                    h = int(detection[3] * height)
                    
                    x = int(center_x - w / 2)
                    y = int(center_y - h / 2)
                    
                    class_name = class_names[class_id] if class_id < len(class_names) else "unknown"
                    
                    detections.append({
                        'class_name': class_name,
                        'confidence': float(confidence),
                        'bbox': (x, y, x + w, y + h)
                    })
        
        print(f"✅ Parsed {len(detections)} detections")
        
        if detections:
            print("🎯 Sample detections:")
            for i, det in enumerate(detections[:3]):
                print(f"   {i+1}. {det['class_name']} ({det['confidence']:.3f})")
        else:
            print("⚠️ No detections found (this might be normal for test image)")
        
        return True
        
    except Exception as e:
        print(f"❌ Detection pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_anomaly_system():
    """Test the anomaly detection system"""
    print("\n🚨 Testing Anomaly Detection System...")
    print("=" * 50)
    
    try:
        # Test imports
        from detection.anomaly_system import AnomalyDetectionSystem
        print("✅ Anomaly system import successful")
        
        # Initialize system
        anomaly_system = AnomalyDetectionSystem()
        print("✅ Anomaly system initialized")
        
        # Check if ready
        if anomaly_system.is_ready():
            print("✅ Anomaly system is ready")
        else:
            print("❌ Anomaly system not ready")
            return False
        
        # Test with dummy frame
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        camera_info = {'width': 640, 'height': 480, 'fps': 30}
        
        # Enable system
        anomaly_system.enable()
        print("✅ Anomaly system enabled")
        
        # Process frame
        annotated_frame, detection_info = anomaly_system.process_frame(test_frame, camera_info)
        print("✅ Frame processing successful")
        
        # Check results
        print(f"📊 Detection info keys: {list(detection_info.keys())}")
        print(f"🎯 Enabled: {detection_info.get('enabled', False)}")
        print(f"🚨 Anomaly detected: {detection_info.get('anomaly_detected', False)}")
        print(f"📋 Detections: {len(detection_info.get('detections', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Anomaly system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_camera():
    """Test camera functionality"""
    print("\n📷 Testing Camera...")
    print("=" * 50)
    
    try:
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Cannot open camera")
            return False
        
        print("✅ Camera opened successfully")
        
        # Try to read a frame
        ret, frame = cap.read()
        if ret:
            height, width = frame.shape[:2]
            print(f"✅ Frame captured: {width}x{height}")
        else:
            print("❌ Cannot read frame from camera")
            cap.release()
            return False
        
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ Camera test failed: {e}")
        return False

def generate_fix_suggestions(test_results):
    """Generate fix suggestions based on test results"""
    print("\n🔧 FIX SUGGESTIONS")
    print("=" * 50)
    
    if not test_results['model_files']:
        print("❌ YOLO Model Files Missing:")
        print("   1. Download yolov3.weights from: https://pjreddie.com/media/files/yolov3.weights")
        print("   2. Download yolov3.cfg from: https://github.com/pjreddie/darknet/blob/master/cfg/yolov3.cfg")
        print("   3. Download coco.names from: https://github.com/pjreddie/darknet/blob/master/data/coco.names")
        print("   4. Place all files in the 'models/' folder")
    
    if not test_results['opencv_dnn']:
        print("❌ OpenCV DNN Issues:")
        print("   1. Update OpenCV: pip install --upgrade opencv-python")
        print("   2. Check OpenCV version: python -c \"import cv2; print(cv2.__version__)\"")
        print("   3. Ensure OpenCV >= 4.0")
    
    if not test_results['detection_pipeline']:
        print("❌ Detection Pipeline Issues:")
        print("   1. Check model file integrity")
        print("   2. Verify OpenCV DNN support")
        print("   3. Check available memory")
    
    if not test_results['anomaly_system']:
        print("❌ Anomaly System Issues:")
        print("   1. Run: python install_anomaly_dependencies.py")
        print("   2. Check YOLO model files")
        print("   3. Verify all imports work")
    
    if not test_results['camera']:
        print("❌ Camera Issues:")
        print("   1. Check camera permissions")
        print("   2. Close other applications using camera")
        print("   3. Try different camera index (1, 2, etc.)")
    
    print("\n✅ QUICK FIXES TO TRY:")
    print("   1. Run: python install_anomaly_dependencies.py")
    print("   2. Download missing YOLO model files")
    print("   3. Restart your application")
    print("   4. Check console output for specific error messages")

def main():
    """Run all diagnostic tests"""
    print("🔍 OBJECT DETECTION & ANOMALY DETECTION DIAGNOSTIC")
    print("=" * 60)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    
    # Run tests
    test_results = {
        'model_files': check_model_files(),
        'class_names': test_class_names(),
        'opencv_dnn': test_opencv_dnn(),
        'detection_pipeline': test_detection_pipeline(),
        'anomaly_system': test_anomaly_system(),
        'camera': test_camera()
    }
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 50)
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your detection systems should work.")
    else:
        print("⚠️ Some tests failed. See fix suggestions below.")
        generate_fix_suggestions(test_results)
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
