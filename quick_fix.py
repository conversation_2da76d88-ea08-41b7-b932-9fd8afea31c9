#!/usr/bin/env python3
"""
Quick fix script to create missing __init__.py files
"""

import os
from pathlib import Path

def create_init_files():
    """Create missing __init__.py files"""
    
    # Directories that need __init__.py files
    packages = ['gui', 'utils', 'detection', 'recording']
    
    for package in packages:
        # Create directory if it doesn't exist
        Path(package).mkdir(exist_ok=True)
        
        # Create __init__.py file
        init_file = Path(package) / '__init__.py'
        init_file.touch(exist_ok=True)
        print(f"✅ Created {init_file}")
    
    print("\n🎉 All __init__.py files created!")
    print("Now you can run: python main.py")

if __name__ == "__main__":
    create_init_files()