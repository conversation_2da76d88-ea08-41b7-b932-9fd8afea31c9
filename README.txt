================================================================================
AI DETECTION TOOL - COMPREHENSIVE USER MANUAL
================================================================================

COMPREHENSIVE REAL-TIME AI DETECTION TOOL
Last Updated: June 17, 2025

KEY FEATURES:
• Advanced button interface with comprehensive user experience
• Detailed tooltips and visual feedback
• Corrected safety warnings for destructive actions
• Improved accessibility and keyboard navigation
• Professional blue theme throughout

================================================================================
TABLE OF CONTENTS
================================================================================

1. OVERVIEW & FEATURES
2. TOOL REQUIREMENTS
3. INSTALLATION & SETUP
4. GETTING STARTED
5. DETECTION SYSTEMS
6. DASHBOARD INTERFACE
7. DATA MANAGEMENT
8. EXPORT & REPORTING
9. TROUBLESHOOTING
10. ADVANCED FEATURES
11. SAFETY & BACKUP
12. SUPPORT & CONTACT

================================================================================
1. TOOL OVERVIEW
================================================================================

The AI Detection Tool is a comprehensive, detection platform
that combines multiple AI technologies into a unified interface. This tool
provides real-time detection capabilities with advanced data management and
analytics features.

CORE TOOL FEATURES:
• Multi-modal AI detection (expressions, age, objects, anomalies)
• Real-time processing with professional-grade accuracy
• Comprehensive dashboard with advanced analytics
• User interface with theme
• Data management with safety features
• Reporting and export capabilities
• SQLite database integration with session tracking

ADVANCED USER EXPERIENCE:
• Button interface with comprehensive tooltips
• Visual feedback with loading states and success indicators
• Multiple confirmation dialogs for destructive actions
• Professional blue theme throughout interface
• Full accessibility support with keyboard navigation
• Corrected safety warnings emphasizing permanent deletions
• Success indicators show "COMPLETE" after operations
• Confirmation dialogs prevent accidental destructive actions

================================================================================
1. OVERVIEW & FEATURES
================================================================================

The AI Detection Dashboard is a comprehensive real-time detection tool that
provides advanced computer vision capabilities with an intuitive interface.

CORE DETECTION CAPABILITIES:
• Age Detection - Real-time age estimation with confidence scoring
• Object Detection - Multi-object recognition with bounding boxes
• Facial Expression Detection - Emotion analysis using YOLOv8 models
• Anomaly Detection - Unusual behavior and threat identification

DASHBOARD FEATURES:
• Real-time statistics and analytics
• Interactive charts and visualizations
• Comprehensive data filtering and search
• PDF report generation
• CSV/JSON data export capabilities
• Automated database management

ADVANCED USER INTERFACE:
• Button interface with comprehensive user experience
• Hover effects, loading states, and success indicators
• Comprehensive tooltips with detailed usage guidance
• Multiple confirmation dialogs for destructive actions
• Progress indicators and visual feedback for all operations
• Full accessibility support with keyboard navigation
• Professional visual design with intuitive interactions

================================================================================
2. TOOL REQUIREMENTS
================================================================================

MINIMUM REQUIREMENTS:
• Operating System: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
• Python: 3.8 or higher
• RAM: 8GB minimum (16GB recommended)
• Storage: 5GB free space
• Camera: USB webcam or built-in camera (for live detection)

RECOMMENDED SPECIFICATIONS:
• CPU: Intel i5/AMD Ryzen 5 or better
• GPU: NVIDIA GTX 1060 or better (for improved performance)
• RAM: 16GB or more
• Storage: SSD with 10GB+ free space

REQUIRED PYTHON PACKAGES:
• OpenCV (cv2) - Computer vision operations
• NumPy - Numerical computations
• Tkinter - GUI framework (usually included with Python)
• Pillow (PIL) - Image processing
• ReportLab - PDF generation
• SQLite3 - Database operations (included with Python)
• YOLOv8 (ultralytics) - Object and expression detection

================================================================================
3. INSTALLATION & SETUP
================================================================================

STEP 1: DOWNLOAD AND EXTRACT
1. Download the AI Detection Dashboard package
2. Extract to your desired directory (e.g., C:\AI_Detection_Dashboard)
3. Ensure all files are properly extracted

STEP 2: INSTALL PYTHON DEPENDENCIES
Open command prompt/terminal in the project directory and run:

    pip install opencv-python numpy pillow reportlab ultralytics

STEP 3: DOWNLOAD REQUIRED MODELS
Run the model download script:

    python download_required_models.py

This will automatically download:
• Age detection models (Caffe format)
• YOLOv8 expression detection model
• Object detection models

STEP 4: VERIFY INSTALLATION
Test the installation:

    python test_installation.py

If successful, you'll see "Installation verified successfully!"

================================================================================
4. GETTING STARTED
================================================================================

LAUNCHING THE DASHBOARD:
1. Open command prompt/terminal in the project directory
2. Run: python gui/enhanced_dashboard.py
3. The dashboard will open with real-time data display

FIRST-TIME SETUP:
• The tool will automatically create a SQLite database
• Initial data migration will be performed
• Default settings will be configured

BASIC NAVIGATION:
• Main Dashboard: Overview of all detection statistics
• Filter Controls: Adjust time ranges and detection types
• Action Buttons: Access export, delete, and refresh functions
• Analytics Tab: View detailed charts and data analysis

================================================================================
3. MAIN DETECTION FEATURES
================================================================================

FACIAL EXPRESSION DETECTION:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

SUPPORTED EXPRESSIONS:
• Happy, Sad, Angry, Fear, Surprise, Disgust, Neutral

KEY FEATURES:
• Custom YOLOv8 model (emotion_detection_83.6_percent.pt)
• 83.6% accuracy on validation dataset
• Real-time face capture and display (140x140 pixels)
• Single consolidated popup windows with auto-close
• Comprehensive database logging with session tracking

AGE ESTIMATION:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CAPABILITIES:
• Precise age estimation with confidence scoring
• Age range categorization (e.g., 25-35 years)
• Temporal smoothing for stable results
• Improved preprocessing for better accuracy
• >3 FPS performance with quality assessment

INTEGRATION:
• Seamless integration with expression detection
• Age overlay boxes positioned near face bounding boxes
• Real-time display of age number, range, and confidence
• Caffe model compatibility maintained

OBJECT DETECTION:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

DETECTION CLASSES:
• Human detection (preferred label over "person")
• 80+ object types including furniture, vehicles, electronics
• Real-time bounding box visualization
• Confidence-based filtering and quality assessment

FEATURES:
• YOLOv8-based object detection with custom human classification
• Real-time processing with professional accuracy
• Comprehensive database logging with coordinates
• Integration with dashboard analytics

ANOMALY DETECTION:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

THREAT ASSESSMENT:
• Advanced anomaly pattern recognition
• Threat level classification and scoring
• Automatic recording of suspicious events
• Comprehensive reporting and alert system

CAPABILITIES:
• Real-time monitoring with automatic alerts
• Video recording during anomaly events
• Detailed threat analysis and reporting
• Integration with main dashboard for comprehensive overview

================================================================================
5. DETECTION SYSTEMS
================================================================================

AGE DETECTION:
• Real-time age estimation from camera feed
• Confidence scoring and quality assessment
• Age range categorization (Child, Teen, Adult, Senior)
• Temporal smoothing for stable results

FEATURES:
- Hover over age display for detailed information
- Confidence threshold: 60%+ for reliable detection
- Processing time: <100ms per frame
- Accuracy: 83.6% on test datasets

OBJECT DETECTION:
• Multi-object recognition in real-time
• 80+ object classes supported (COCO dataset)
• Bounding box visualization
• Confidence scoring for each detection

SUPPORTED OBJECTS:
- People, vehicles, animals, furniture, electronics
- Sports equipment, food items, household objects
- Custom "human" class 

FACIAL EXPRESSION DETECTION:
• Emotion recognition using custom YOLOv8 model
• 7 emotion classes: Happy, Sad, Angry, Surprised, Fear, Disgust, Neutral
• Face capture and display in popup windows
• Model: emotion_detection_83.6_percent.pt

FEATURES:
- Single consolidated popup per detection
- Face image display (140x140 pixels)
- Auto-close functionality (5 seconds)
- Professional blue theme interface

ANOMALY DETECTION:
• Unusual behavior identification
• Threat level assessment
• Automatic recording of anomalous events
• Report generation for security analysis

================================================================================
6. DASHBOARD INTERFACE
================================================================================

MAIN COMPONENTS:

HEADER SECTION:
• Tool status indicator (ACTIVE)
• Current date and time display
• Session information

STATISTICS CARDS:
• Age Detections: Total age detection count
• Objects: Total object detection count
• Expressions: Total expression detection count
• Anomalies: Total anomaly detection count
• Confidence: Average confidence percentage

FILTER CONTROLS:
• Detection Type Filters: Enable/disable specific detection types
• Time Range Selection: Last Hour, 6 Hours, 24 Hours, 7 Days, All Time
• Auto-refresh Toggle: Enable automatic data updates (5-second interval)

ADVANCED ACTION BUTTONS:
All buttons feature advanced user experience improvements:

REFRESH BUTTON:
• Function: Updates dashboard with latest database data
• Visual Feedback: "Refreshing..." → "Refreshed" (success indicator)
• Tooltip: "Refresh dashboard data from database
           Updates all statistics and charts"
• Features: Loading state, success indicator, hover effects, confirmation dialog

PDF REPORT BUTTON:
• Function: Generates comprehensive PDF analysis reports
• Visual Feedback: "Generating..." → "PDF Created" (success indicator)
• Tooltip: "Generate comprehensive PDF report
           Includes all detection data and analytics"
• Features: Progress indication, professional formatting, confirmation dialog

EXPORT CSV BUTTON:
• Function: Exports all data in structured CSV format
• Visual Feedback: "Exporting..." → "CSV Exported"
• Tooltip: "Export detection data to CSV file
           Includes timestamps and confidence scores"
• Features: Comprehensive metadata, Excel compatibility

DELETE RECORDS BUTTON:
• Function: Opens advanced deletion dialog with filters
• SAFETY WARNING: "DELETIONS ARE PERMANENT AND CANNOT BE UNDONE!"
• Tooltip: "Delete detection records with filters
           THIS ACTION CANNOT BE UNDONE!"
• Features: Multiple confirmations, real-time preview, safety checks

CLEAR OLD DATA BUTTON:
• Function: Opens data cleanup dialog for old records
• SAFETY WARNING: "DATA REMOVAL IS PERMANENT AND CANNOT BE UNDONE!"
• Tooltip: "Remove old detection records
           THIS ACTION CANNOT BE UNDONE!"
• Features: Age-based cleanup, performance optimization, backup reminders

ANALYTICS SECTION:
• Overview Tab: Visual charts and graphs
• Details Tab: Comprehensive data table with sorting and filtering

================================================================================
7. ADVANCED DATA MANAGEMENT
================================================================================

ADVANCED DELETION INTERFACE:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

DELETION DIALOG FEATURES:
• Advanced 800x700 resizable interface with professional design
• Real-time preview showing exact records to be deleted
• Multiple filter options for precise control
• Comprehensive safety warnings and confirmations

FILTER OPTIONS:

Detection Type Selection:
- Age Detections (shows current database count)
- Object Detections (shows current database count)
- Expression Detections (shows current database count)
- Anomaly Detections (shows current database count)

Confidence Threshold Controls:
• Precision slider from 0.0 to 1.0
• 0.0 = Delete ALL selected records (no confidence filter)
• Higher values = Delete only low-confidence records
• Real-time label updates show exactly what will be deleted
• Dynamic color coding (red for all, orange for filtered)

Time Range Filters:
• Last Hour, 6 Hours, 24 Hours, 7 Days
• All Time, or Custom Date Range selection
• Accurate filtering with live preview counts
• Helps target specific time periods for cleanup

REAL-TIME PREVIEW INTERFACE:
• Shows exact number of records that will be deleted
• Updates instantly when any filter changes
• Detailed breakdown by detection type
• Clear warnings about permanent deletion
• Preview prevents accidental mass deletions

COMPREHENSIVE SAFETY MEASURES:
• Multiple confirmation dialogs at each step
• Clear warnings: "THIS ACTION CANNOT BE UNDONE!"
• Emphasis on permanent nature: "CANNOT BE RECOVERED!"
• Backup reminders before proceeding with deletions
• Cancel options available at every step
• Advanced buttons with confirmation requirements

ADVANCED DATA CLEANUP INTERFACE:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CLEANUP OPTIONS:
• Remove data older than 7 days (recent cleanup)
• Remove data older than 30 days (monthly cleanup)
• Remove data older than 90 days (quarterly cleanup)
• Custom date range cleanup for specific periods

BENEFITS & FEATURES:
• Maintains optimal database performance
• Reduces storage requirements significantly
• Keeps recent data for ongoing analysis
• Automatic dashboard refresh after cleanup
• Progress indicators during cleanup operations
• Detailed reporting of cleanup results

CRITICAL SAFETY WARNINGS:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

WARNING: PERMANENT DELETION WARNING:
ALL DELETE AND CLEAR OPERATIONS ARE PERMANENT AND CANNOT BE UNDONE!

• Deleted records are permanently removed from the database
• No recovery options available after deletion
• Data cannot be restored once removed
• Always ensure you have backups before proceeding
• Consider exporting important data before cleanup

SAFETY CONFIRMATIONS:
• Initial button confirmation with warning
• Dialog-level confirmation with detailed warning
• Final execution confirmation with consequences
• Multiple opportunities to cancel operation
• Clear language about permanent nature of actions

DATABASE STRUCTURE:
The system uses SQLite database (detection_results.db) with tables:
• age_detections - Age detection records
• object_detections - Object detection records
• expression_detections - Expression detection records
• anomaly_detections - Anomaly detection records

DATA FILTERING:
• Time Range Filtering: Select specific time periods
• Confidence Filtering: Filter by detection confidence levels
• Type Filtering: Show/hide specific detection types
• Session Filtering: Filter by detection sessions

SEARCH FUNCTIONALITY:
• Text search across all detection data
• Advanced filtering options
• Real-time search results
• Export filtered results

DATA RETENTION:
• Automatic cleanup of old data (configurable)
• Manual data cleanup options
• Backup recommendations before cleanup
• Performance optimization through data management

================================================================================
8. EXPORT & REPORTING
================================================================================

PDF REPORTS:
• Comprehensive analysis reports
• Statistical summaries and trends
• Visual charts and graphs
• Professional formatting with logos
• Customizable date ranges

FEATURES:
- Executive summary with key metrics
- Detailed detection breakdowns
- Confidence analysis and quality metrics
- Temporal distribution analysis
- Data quality assessment

CSV EXPORT:
• Structured data export for analysis
• All detection metadata included
• Timestamp and confidence information
• Compatible with Excel and data analysis tools

JSON EXPORT:
• Machine-readable format
• API-compatible structure
• Nested data relationships
• Ideal for integration with other systems

EXPORT OPTIONS:
• Full dataset export
• Filtered data export
• Date range selection
• Custom field selection

================================================================================
9. TROUBLESHOOTING
================================================================================

COMMON ISSUES:

CAMERA NOT DETECTED:
• Check camera connections
• Verify camera permissions
• Try different camera indices (0, 1, 2)
• Restart the application

MODEL LOADING ERRORS:
• Ensure models are downloaded: python download_required_models.py
• Check internet connection during download
• Verify model file integrity
• Clear model cache and re-download

DATABASE ERRORS:
• Check file permissions in project directory
• Ensure SQLite is properly installed
• Run database verification: python check_database_schema.py
• Backup and recreate database if corrupted

PERFORMANCE ISSUES:
• Close unnecessary applications
• Reduce detection resolution
• Adjust confidence thresholds
• Enable GPU acceleration if available

INTERFACE PROBLEMS:
• Update Python and Tkinter
• Check display scaling settings
• Verify all dependencies are installed
• Restart with administrator privileges

================================================================================
10. ADVANCED FEATURES
================================================================================

CONFIDENCE THRESHOLDS:
• Adjustable confidence levels for each detection type
• Quality-based filtering
• Performance optimization
• Accuracy vs. speed trade-offs

TEMPORAL SMOOTHING:
• Stable detection results over time
• Noise reduction in age detection
• Consistent object tracking
• Expression stability improvement

BATCH PROCESSING:
• Process multiple images/videos
• Automated analysis workflows
• Scheduled detection tasks
• Bulk data processing

CUSTOM MODELS:
• Support for custom YOLOv8 models
• Model training integration
• Performance benchmarking
• Model comparison tools

API INTEGRATION:
• REST API endpoints
• JSON data exchange
• External tool integration
• Real-time data streaming

================================================================================
11. SAFETY & BACKUP
================================================================================

CRITICAL DATA SAFETY INFORMATION:
WARNING: EXTREMELY IMPORTANT: Delete and clear operations are PERMANENT and CANNOT be undone!

ADVANCED SAFETY MEASURES:
• All destructive actions require multiple confirmations
• Clear warnings state "CANNOT BE UNDONE" and "CANNOT BE RECOVERED"
• Advanced buttons include safety tooltips and confirmations
• Real-time preview shows exactly what will be deleted
• Multiple opportunities to cancel before execution

COMPREHENSIVE BACKUP RECOMMENDATIONS:
• Daily database backups for active systems
• Weekly full tool backups (recommended minimum)
• Export critical data before any cleanup operations
• Keep model files backed up in separate location
• Document all custom configurations and modifications
• Test backup restoration procedures regularly

DETAILED BACKUP PROCEDURES:
1. Copy detection_results.db to secure backup location
2. Export all critical data as CSV/JSON before cleanup
3. Backup models folder with all AI model files
4. Save configuration files and custom settings
5. Document any custom modifications or integrations
6. Verify backup integrity before proceeding with deletions
7. Store backups in multiple locations (local + cloud recommended)

SECURITY CONSIDERATIONS:
• Protect sensitive detection data
• Secure database access
• Regular security updates
• Access control implementation

================================================================================
12. SUPPORT & CONTACT
================================================================================

GETTING HELP:
• Check this manual first
• Run diagnostic tools: python diagnose_dashboard_issues.py
• Review log files for error details
• Test with provided examples

DOCUMENTATION:
• README.txt - This comprehensive manual
• ENHANCED_DASHBOARD_SUMMARY.md - Technical overview
• Code comments - Inline documentation
• Test files - Usage examples

COMPREHENSIVE TROUBLESHOOTING TOOLS:
• test_installation.py - Verify complete tool setup
• test_enhanced_buttons.py - Test advanced button functionality
• test_enhanced_delete_refresh.py - Verify delete and refresh operations
• test_corrected_warnings.py - Verify safety warning messages
• test_database_integration.py - Check database connectivity
• diagnose_dashboard_issues.py - Complete tool diagnostics

PERFORMANCE & FUNCTIONALITY TESTING:
• test_enhanced_analytics.py - Analytics and reporting verification
• test_real_delete_functionality.py - Data operations testing
• test_final_dashboard.py - Complete tool integration test
• test_enhanced_models.py - AI model functionality verification

TESTING TOOLS:
• test_enhanced_buttons.py - Comprehensive button functionality testing
• test_corrected_warnings.py - Safety warning message verification
• Advanced button functionality demos and validation

================================================================================
CONGRATULATIONS!
================================================================================

You now have access to a comprehensive AI Detection Dashboard with:
- Real-time multi-modal detection capabilities
- Professional data management with advanced safety features
- Advanced button interface with comprehensive user experience
- Comprehensive analytics and professional reporting
- Advanced export capabilities and backup options
- Professional blue theme interface
- Multiple safety confirmations for destructive actions
- Comprehensive tooltips and visual feedback
- Full accessibility support and keyboard navigation

ENJOY USING YOUR AI DETECTION TOOL!

FOR THE BEST EXPERIENCE:
• Keep your tool updated with latest models
• Regularly backup your detection data (CRITICAL!)
• Monitor performance metrics and tool health
• Explore advanced features and customization options
• Use the advanced button tooltips for guidance
• Always confirm you have backups before deletions
• Take advantage of the real-time preview features

WARNING: REMEMBER: All delete and clear operations are PERMANENT and CANNOT be undone!

KEY HIGHLIGHTS:
• Advanced button interface with comprehensive user experience
• Corrected safety warnings for all destructive actions
• Advanced tooltips with detailed usage guidance
• Professional visual feedback and loading states
• Multiple confirmation dialogs for user safety
• Improved accessibility and keyboard navigation

================================================================================
12. TECHNICAL SPECIFICATIONS
================================================================================

AI MODELS & PERFORMANCE:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

EXPRESSION DETECTION:
• Model: emotion_detection_83.6_percent.pt (YOLOv8-based)
• Accuracy: 83.6% on validation dataset
• Classes: 7 emotions (happy, sad, angry, fear, surprise, disgust, neutral)
• Performance: Real-time processing with face capture
• Input: RGB camera feed, 640x640 optimal resolution
• Output: Emotion classification with confidence scores

AGE ESTIMATION:
• Model: Caffe-based age estimation with YOLOv8 integration
• Performance: >3 FPS with temporal smoothing
• Features: Quality assessment and stability scoring
• Range: 0-100 years with categorical ranges
• Accuracy: Improved preprocessing for better results

OBJECT DETECTION:
• Model: YOLOv8 with custom human classification
• Classes: 80+ COCO dataset objects with human preference
• Performance: Real-time bounding box detection
• Features: Confidence-based filtering and NMS
• Input: RGB camera feed, multiple resolutions supported

DATABASE ARCHITECTURE:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

DATABASE ENGINE: SQLite 3.x
• File-based database for portability
• ACID compliance for data integrity
• Automatic schema migrations
• Comprehensive indexing for performance

TABLES SCHEMA:
• age_detections: Age estimation results with confidence
• expression_detections: Facial expression data with face images
• object_detections: Object detection with bounding boxes
• anomaly_detections: Threat assessment and alert data

FEATURES:
• Session tracking for data organization
• Automatic timestamps with timezone support
• Data integrity constraints and foreign keys
• Optimized queries with proper indexing

USER INTERFACE TECHNOLOGY:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

FRAMEWORK: Python Tkinter with custom improvements
• Advanced button interface with comprehensive UX
• Professional blue theme (#2E86AB) with consistent styling
• Custom tooltip interface with auto-positioning
• Advanced visual feedback and loading states

ADVANCED FEATURES:
• Hover effects with smooth color transitions
• Loading states with progress indicators
• Success feedback with visual confirmations
• Multiple confirmation dialogs for safety
• Full accessibility support with keyboard navigation

DESIGN PRINCIPLES:
• Consistent professional blue theme throughout
• Professional appearance with intuitive navigation
• User safety with multiple confirmations
• Clear visual hierarchy and information architecture

SYSTEM REQUIREMENTS:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

MINIMUM REQUIREMENTS:
• Operating System: Windows 10/11 (primary), Linux/macOS (compatible)
• Python: 3.8 or higher with pip package manager
• Memory: 4GB RAM minimum (8GB recommended for optimal performance)
• Storage: 2GB free disk space for models and data
• Camera: USB webcam or integrated camera with 720p minimum resolution

RECOMMENDED SPECIFICATIONS:
• CPU: Intel i5 or AMD Ryzen 5 equivalent or better
• Memory: 8GB RAM or higher for smooth operation
• Storage: SSD for improved database performance
• Camera: 1080p webcam for best detection accuracy
• Network: Internet connection for model downloads

DEPENDENCIES:
• OpenCV: Computer vision and camera handling
• YOLOv8: Object and expression detection models
• NumPy: Numerical computations and array operations
• Pillow: Image processing and manipulation
• Matplotlib: Chart generation for analytics
• SQLite3: Database operations (included with Python)

================================================================================
© 2025 AI Detection Tool - Professional Documentation
================================================================================
