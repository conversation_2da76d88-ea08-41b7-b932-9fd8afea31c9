import cv2
import os
from datetime import datetime
import shutil

class VideoRecorder:
    def __init__(self):
        self.writer = None
        self.recording = False
        self.start_time = None
        self.base_dir = "Security_Footage"
        self.current_folder = None
        self.max_folders = 10
        
        os.makedirs(self.base_dir, exist_ok=True)
    
    def start_recording(self, width, height):
        if self.recording:
            return
        
        # Create new folder for recording
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.current_folder = os.path.join(self.base_dir, f"Detection_{timestamp}")
        os.makedirs(self.current_folder, exist_ok=True)
        
        # Manage folder count
        self.cleanup_old_folders()
        
        # Initialize video writer
        video_path = os.path.join(self.current_folder, "recording.mp4")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        self.writer = cv2.VideoWriter(video_path, fourcc, 20.0, (width, height))
        
        self.recording = True
        self.start_time = datetime.now()
    
    def record_frame(self, frame):
        if self.recording and self.writer:
            self.writer.write(frame)
    
    def stop_recording(self):
        if self.recording:
            if self.writer:
                self.writer.release()
                self.writer = None
            
            # Save metadata
            duration = (datetime.now() - self.start_time).total_seconds()
            with open(os.path.join(self.current_folder, "metadata.txt"), "w") as f:
                f.write(f"Start Time: {self.start_time}\n")
                f.write(f"Duration: {duration:.2f} seconds\n")
            
            self.recording = False
            self.current_folder = None
    
    def is_recording(self):
        return self.recording
    
    def cleanup_old_folders(self):
        folders = [os.path.join(self.base_dir, f) for f in os.listdir(self.base_dir) 
                  if os.path.isdir(os.path.join(self.base_dir, f))]
        
        if len(folders) > self.max_folders:
            # Sort by creation time and remove oldest
            folders.sort(key=lambda x: os.path.getctime(x))
            for folder in folders[:len(folders) - self.max_folders]:
                shutil.rmtree(folder)