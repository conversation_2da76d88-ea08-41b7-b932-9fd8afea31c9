"""
Fix script to update all "person" detections to "human" in the database
This ensures consistency throughout the system
"""

import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_person_to_human():
    """Update all 'person' detections to 'human' in the database"""
    print("🔧 FIXING PERSON TO HUMAN IN DATABASE")
    print("=" * 50)
    
    try:
        from utils.database_integration import get_database
        
        # Get database instance
        db = get_database()
        print(f"✅ Database connected: {db.db_path}")
        
        # Check current counts
        object_records = db.get_recent_detections('object', limit=10000)
        person_count = sum(1 for record in object_records if record.get('object_name') == 'person')
        human_count = sum(1 for record in object_records if record.get('object_name') == 'human')
        
        print(f"📊 Before fix:")
        print(f"   'person' detections: {person_count}")
        print(f"   'human' detections: {human_count}")
        
        if person_count == 0:
            print("✅ No 'person' detections found - database is already correct!")
            return True
        
        # Update person to human
        print(f"🔄 Updating {person_count} 'person' detections to 'human'...")
        
        # Direct database update
        import sqlite3
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        # Update all 'person' to 'human'
        cursor.execute("""
            UPDATE object_detections 
            SET object_name = 'human' 
            WHERE object_name = 'person'
        """)
        
        updated_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"✅ Updated {updated_count} records from 'person' to 'human'")
        
        # Verify the fix
        object_records_after = db.get_recent_detections('object', limit=10000)
        person_count_after = sum(1 for record in object_records_after if record.get('object_name') == 'person')
        human_count_after = sum(1 for record in object_records_after if record.get('object_name') == 'human')
        
        print(f"📊 After fix:")
        print(f"   'person' detections: {person_count_after}")
        print(f"   'human' detections: {human_count_after}")
        
        if person_count_after == 0:
            print("✅ Fix successful! All detections now use 'human'")
            return True
        else:
            print("❌ Fix incomplete - some 'person' detections remain")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing person to human: {e}")
        return False

def verify_dashboard_after_fix():
    """Verify dashboard shows correct data after fix"""
    print("\n🎯 VERIFYING DASHBOARD AFTER FIX")
    print("=" * 40)
    
    try:
        from gui.enhanced_dashboard import StreamlinedDashboard
        
        # Create dashboard instance
        dashboard = StreamlinedDashboard()
        
        if dashboard.enhanced_db:
            print("✅ Dashboard connected to database")
            
            # Get current counts
            counts = dashboard.get_current_detection_counts()
            print(f"📊 Dashboard sees: {counts}")
            
            # Test refresh
            dashboard.refresh_data()
            
            # Check object data specifically
            object_data = dashboard.current_data.get('object', [])
            human_count = sum(1 for record in object_data if record.get('object_name') == 'human')
            person_count = sum(1 for record in object_data if record.get('object_name') == 'person')
            
            print(f"🔍 Dashboard object data:")
            print(f"   Total objects: {len(object_data)}")
            print(f"   'human' detections: {human_count}")
            print(f"   'person' detections: {person_count}")
            
            if person_count == 0:
                print("✅ Dashboard correctly shows only 'human' detections")
                return True
            else:
                print("❌ Dashboard still shows 'person' detections")
                return False
        else:
            print("❌ Dashboard not connected to database")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard verification failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 PERSON TO HUMAN DATABASE FIX")
    print("=" * 50)
    print("This script updates all 'person' detections to 'human' for consistency")
    print("=" * 50)
    
    # Run the fix
    fix_result = fix_person_to_human()
    
    if fix_result:
        # Verify dashboard
        verify_result = verify_dashboard_after_fix()
        
        if verify_result:
            print("\n🎉 SUCCESS! Database and dashboard now use 'human' consistently")
            print("✅ All 'person' detections updated to 'human'")
            print("✅ Dashboard displays correct data")
            print("✅ Ready for accurate real-time statistics")
        else:
            print("\n⚠️ Database fixed but dashboard verification failed")
    else:
        print("\n❌ Database fix failed")
    
    print("\n🔧 Person to human fix completed!")
