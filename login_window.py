import tkinter as tk
from tkinter import messagebox, ttk
import os
import sys
import time
import threading
from datetime import datetime

# Ensure we can import from parent directories
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

try:
    from utils.config import Config
    from utils.logger import setup_logger
    from utils.security_manager import SecurityManager
except ImportError:
    # Fallback configuration
    class Config:
        DEFAULT_USERNAME = "admin"
        DEFAULT_PASSWORD = "password123"
        WINDOW_SIZE = (1200, 800)
    
    def setup_logger():
        import logging
        return logging.getLogger(__name__)
    
    class SecurityManager:
        def authenticate_user(self, username, password):
            return username == "admin" and password == "password123", "Success", {"role": "admin"}


class ProfessionalButton:
    """Professional styled button with hover effects and animations"""
    
    def __init__(self, parent, text, command=None, style="primary", width=200, height=45):
        self.parent = parent
        self.text = text
        self.command = command
        self.width = width
        self.height = height
        self.enabled = True
        
        # Professional color schemes
        self.styles = {
            "primary": {
                "bg": "#0066CC",
                "hover_bg": "#0052A3",
                "active_bg": "#003D7A", 
                "text_color": "white",
                "border": "#0066CC"
            },
            "secondary": {
                "bg": "#6C757D",
                "hover_bg": "#5A6268",
                "active_bg": "#495057",
                "text_color": "white", 
                "border": "#6C757D"
            },
            "success": {
                "bg": "#28A745",
                "hover_bg": "#218838",
                "active_bg": "#1E7E34",
                "text_color": "white",
                "border": "#28A745"
            },
            "danger": {
                "bg": "#DC3545",
                "hover_bg": "#C82333",
                "active_bg": "#BD2130",
                "text_color": "white",
                "border": "#DC3545"
            }
        }
        
        self.current_style = self.styles[style]
        
        # Create button frame
        self.frame = tk.Frame(parent, bg=parent.cget('bg'))
        
        # Create canvas for custom drawing
        self.canvas = tk.Canvas(
            self.frame,
            width=width,
            height=height,
            highlightthickness=0,
            bd=0,
            bg=parent.cget('bg')
        )
        self.canvas.pack()
        
        self.draw_button()
        self.bind_events()
    
    def draw_button(self):
        """Draw the professional button"""
        self.canvas.delete("all")
        
        bg_color = self.current_style["bg"] if self.enabled else "#E9ECEF"
        text_color = self.current_style["text_color"] if self.enabled else "#6C757D"
        
        # Draw rounded rectangle background
        self.create_rounded_rectangle(
            2, 2, self.width-2, self.height-2, 
            radius=8, fill=bg_color, outline=""
        )
        
        # Add subtle border
        self.create_rounded_rectangle(
            1, 1, self.width-1, self.height-1,
            radius=8, fill="", outline="#DEE2E6", width=1
        )
        
        # Draw text
        self.canvas.create_text(
            self.width//2, self.height//2,
            text=self.text,
            fill=text_color,
            font=("Segoe UI", 11, "bold"),
            anchor="center"
        )
        
        # Add subtle shadow effect
        if self.enabled:
            self.create_rounded_rectangle(
                3, 4, self.width-1, self.height,
                radius=8, fill="#00000015", outline=""
            )
    
    def create_rounded_rectangle(self, x1, y1, x2, y2, radius=5, **kwargs):
        """Create a rounded rectangle"""
        points = []
        for x, y in [(x1, y1 + radius), (x1, y1), (x1 + radius, y1),
                     (x2 - radius, y1), (x2, y1), (x2, y1 + radius),
                     (x2, y2 - radius), (x2, y2), (x2 - radius, y2),
                     (x1 + radius, y2), (x1, y2), (x1, y2 - radius)]:
            points.extend([x, y])
        return self.canvas.create_polygon(points, smooth=True, **kwargs)
    
    def bind_events(self):
        """Bind mouse events"""
        self.canvas.bind("<Button-1>", self.on_click)
        self.canvas.bind("<Enter>", self.on_enter)
        self.canvas.bind("<Leave>", self.on_leave)
        self.canvas.bind("<ButtonRelease-1>", self.on_release)
    
    def on_enter(self, event):
        """Handle mouse enter"""
        if self.enabled:
            self.current_style["bg"] = self.current_style["hover_bg"]
            self.draw_button()
            self.canvas.configure(cursor="hand2")
    
    def on_leave(self, event):
        """Handle mouse leave"""
        if self.enabled:
            self.current_style["bg"] = self.styles[list(self.styles.keys())[0]]["bg"]
            self.draw_button()
            self.canvas.configure(cursor="")
    
    def on_click(self, event):
        """Handle click"""
        if self.enabled:
            self.current_style["bg"] = self.current_style["active_bg"]
            self.draw_button()
    
    def on_release(self, event):
        """Handle release"""
        if self.enabled and self.command:
            self.command()
            # Reset to hover state
            self.current_style["bg"] = self.current_style["hover_bg"]
            self.draw_button()
    
    def pack(self, **kwargs):
        """Pack the button frame"""
        return self.frame.pack(**kwargs)
    
    def configure(self, **kwargs):
        """Configure button properties"""
        if 'state' in kwargs:
            self.enabled = kwargs['state'] != 'disabled'
            self.draw_button()


class LoadingIndicator:
    """Professional loading indicator"""
    
    def __init__(self, parent):
        self.parent = parent
        self.frame = tk.Frame(parent, bg=parent.cget('bg'))
        
        self.canvas = tk.Canvas(
            self.frame,
            width=40,
            height=40,
            bg=parent.cget('bg'),
            highlightthickness=0
        )
        self.canvas.pack()
        
        self.angle = 0
        self.running = False
        
    def start(self):
        """Start the loading animation"""
        self.running = True
        self.animate()
    
    def stop(self):
        """Stop the loading animation"""
        self.running = False
        self.canvas.delete("all")
    
    def animate(self):
        """Animate the loading indicator"""
        if not self.running:
            return
            
        self.canvas.delete("all")
        
        # Draw spinning circle
        x, y = 20, 20
        radius = 15
        
        # Draw background circle
        self.canvas.create_oval(
            x-radius, y-radius, x+radius, y+radius,
            outline="#E9ECEF", width=3
        )
        
        # Draw animated arc
        extent = 90
        self.canvas.create_arc(
            x-radius, y-radius, x+radius, y+radius,
            start=self.angle, extent=extent,
            outline="#0066CC", width=3, style="arc"
        )
        
        self.angle = (self.angle + 10) % 360
        self.parent.after(50, self.animate)
    
    def pack(self, **kwargs):
        return self.frame.pack(**kwargs)


class ProfessionalLoginWindow:
    """Enhanced professional login window with modern design"""
    
    def __init__(self):
        self.config = Config()
        self.security_manager = SecurityManager()
        
        try:
            self.logger = setup_logger()
            self.logger.info("Starting professional login interface...")
        except:
            print("Starting professional login interface...")
        
        self.root = tk.Tk()
        self.authenticated = False
        self.loading = False
        
        # Initialize UI components
        self.setup_window()
        self.create_professional_interface()
        
        # Security features
        self.failed_attempts = 0
        self.max_attempts = 3
        self.lockout_time = 0
    
    def setup_window(self):
        """Setup main window with professional styling"""
        self.root.title("AI Video Detection System - Secure Access")
        self.root.geometry("1000x700")
        self.root.configure(bg='#F8F9FA')
        self.root.resizable(False, False)
        
        # Center window
        self.center_window()
        
        # Set professional window properties
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Configure styles
        self.setup_styles()
    
    def setup_styles(self):
        """Setup professional ttk styles"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure professional entry style
        self.style.configure(
            "Professional.TEntry",
            fieldbackground="white",
            borderwidth=2,
            relief="solid",
            bordercolor="#DEE2E6",
            focuscolor="#0066CC",
            font=("Segoe UI", 11)
        )
        
        # Configure professional label style
        self.style.configure(
            "Professional.TLabel",
            background="#F8F9FA",
            font=("Segoe UI", 11)
        )
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")
    
    def create_professional_interface(self):
        """Create the professional login interface"""
        # Main container
        main_container = tk.Frame(self.root, bg='#F8F9FA')
        main_container.pack(fill='both', expand=True)
        
        # Create split layout
        self.create_branding_panel(main_container)
        self.create_login_panel(main_container)
        
        # Status bar
        self.create_status_bar()
    
    def create_branding_panel(self, parent):
        """Create left branding panel"""
        branding_panel = tk.Frame(parent, bg='#0066CC', width=400)
        branding_panel.pack(side='left', fill='y')
        branding_panel.pack_propagate(False)
        
        # Branding content
        branding_content = tk.Frame(branding_panel, bg='#0066CC')
        branding_content.pack(expand=True, padx=40, pady=60)
        
        # Company logo area
        logo_frame = tk.Frame(branding_content, bg='#0066CC')
        logo_frame.pack(pady=(0, 40))
        
        # Large logo/icon
        logo_canvas = tk.Canvas(logo_frame, width=100, height=100, bg='#0066CC', highlightthickness=0)
        logo_canvas.pack()
        
        # Draw professional logo
        logo_canvas.create_oval(10, 10, 90, 90, fill='white', outline='')
        logo_canvas.create_text(50, 35, text="🛡️", font=("Segoe UI", 30), fill='#0066CC')
        logo_canvas.create_text(50, 65, text="AI", font=("Segoe UI", 12, "bold"), fill='#0066CC')
        
        # Main title
        title_label = tk.Label(
            branding_content,
            text="AI Video Detection",
            font=("Segoe UI", 28, "bold"),
            bg='#0066CC',
            fg='white'
        )
        title_label.pack(pady=(0, 10))
        
        # Subtitle
        subtitle_label = tk.Label(
            branding_content,
            text="Enterprise Security Platform",
            font=("Segoe UI", 14),
            bg='#0066CC',
            fg='#B3D9FF'
        )
        subtitle_label.pack(pady=(0, 40))
        
        # Features list
        features_frame = tk.Frame(branding_content, bg='#0066CC')
        features_frame.pack(fill='x')
        
        features = [
            "🔒 Advanced Security Analytics",
            "👁️ Real-time Threat Detection", 
            "📊 Comprehensive Reporting",
            "🚨 Instant Alert System",
            "🤖 AI-Powered Recognition"
        ]
        
        for feature in features:
            feature_label = tk.Label(
                features_frame,
                text=feature,
                font=("Segoe UI", 12),
                bg='#0066CC',
                fg='white',
                anchor='w'
            )
            feature_label.pack(fill='x', pady=8)
        
        # Security badges
        security_frame = tk.Frame(branding_content, bg='#0066CC')
        security_frame.pack(side='bottom', fill='x', pady=(40, 0))
        
        security_label = tk.Label(
            security_frame,
            text="🔐 Enterprise Grade Security • 256-bit Encryption • SOC 2 Compliant",
            font=("Segoe UI", 9),
            bg='#0066CC',
            fg='#B3D9FF'
        )
        security_label.pack()
    
    def create_login_panel(self, parent):
        """Create right login panel"""
        login_panel = tk.Frame(parent, bg='#F8F9FA')
        login_panel.pack(side='right', fill='both', expand=True)
        
        # Login container
        login_container = tk.Frame(login_panel, bg='#F8F9FA')
        login_container.pack(expand=True, padx=60, pady=60)
        
        # Header
        self.create_login_header(login_container)
        
        # Login form
        self.create_login_form(login_container)
        
        # Additional options
        self.create_additional_options(login_container)
    
    def create_login_header(self, parent):
        """Create login form header"""
        header_frame = tk.Frame(parent, bg='#F8F9FA')
        header_frame.pack(fill='x', pady=(0, 40))
        
        # Welcome message
        welcome_label = tk.Label(
            header_frame,
            text="Welcome Back",
            font=("Segoe UI", 24, "bold"),
            bg='#F8F9FA',
            fg='#212529'
        )
        welcome_label.pack(anchor='w')
        
        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="Please sign in to your account",
            font=("Segoe UI", 12),
            bg='#F8F9FA',
            fg='#6C757D'
        )
        subtitle_label.pack(anchor='w', pady=(5, 0))
        
        # Security indicator
        security_frame = tk.Frame(header_frame, bg='#F8F9FA')
        security_frame.pack(anchor='w', pady=(15, 0))
        
        security_indicator = tk.Canvas(security_frame, width=12, height=12, bg='#F8F9FA', highlightthickness=0)
        security_indicator.pack(side='left')
        security_indicator.create_oval(2, 2, 10, 10, fill='#28A745', outline='')
        
        security_label = tk.Label(
            security_frame,
            text="Secure Connection Established",
            font=("Segoe UI", 9),
            bg='#F8F9FA',
            fg='#28A745'
        )
        security_label.pack(side='left', padx=(8, 0))
    
    def create_login_form(self, parent):
        """Create the main login form"""
        form_frame = tk.Frame(parent, bg='#F8F9FA')
        form_frame.pack(fill='x', pady=(0, 30))
        
        # Username field
        username_frame = tk.Frame(form_frame, bg='#F8F9FA')
        username_frame.pack(fill='x', pady=(0, 20))
        
        username_label = tk.Label(
            username_frame,
            text="Username",
            font=("Segoe UI", 11, "bold"),
            bg='#F8F9FA',
            fg='#212529'
        )
        username_label.pack(anchor='w', pady=(0, 8))
        
        self.username_entry = ttk.Entry(
            username_frame,
            style="Professional.TEntry",
            font=("Segoe UI", 12),
            width=30
        )
        self.username_entry.pack(fill='x', ipady=8)
        self.username_entry.insert(0, "admin")  # Default for demo
        
        # Password field
        password_frame = tk.Frame(form_frame, bg='#F8F9FA')
        password_frame.pack(fill='x', pady=(0, 20))
        
        password_label = tk.Label(
            password_frame,
            text="Password",
            font=("Segoe UI", 11, "bold"),
            bg='#F8F9FA',
            fg='#212529'
        )
        password_label.pack(anchor='w', pady=(0, 8))
        
        # Password input with show/hide
        password_input_frame = tk.Frame(password_frame, bg='#F8F9FA')
        password_input_frame.pack(fill='x')
        
        self.password_entry = ttk.Entry(
            password_input_frame,
            style="Professional.TEntry",
            font=("Segoe UI", 12),
            show='●',
            width=25
        )
        self.password_entry.pack(side='left', fill='x', expand=True, ipady=8)
        self.password_entry.insert(0, "password123")  # Default for demo
        
        # Show/hide password button
        self.show_password_var = tk.BooleanVar()
        show_password_btn = tk.Checkbutton(
            password_input_frame,
            text="👁️",
            variable=self.show_password_var,
            command=self.toggle_password_visibility,
            font=("Segoe UI", 10),
            bg='#F8F9FA',
            relief='flat',
            bd=0
        )
        show_password_btn.pack(side='right', padx=(10, 0))
        
        # Remember me checkbox
        remember_frame = tk.Frame(form_frame, bg='#F8F9FA')
        remember_frame.pack(fill='x', pady=(0, 25))
        
        self.remember_var = tk.BooleanVar()
        remember_checkbox = tk.Checkbutton(
            remember_frame,
            text="Remember me",
            variable=self.remember_var,
            font=("Segoe UI", 10),
            bg='#F8F9FA',
            fg='#212529'
        )
        remember_checkbox.pack(side='left')
        
        # Forgot password link
        forgot_link = tk.Label(
            remember_frame,
            text="Forgot Password?",
            font=("Segoe UI", 10, "underline"),
            bg='#F8F9FA',
            fg='#0066CC',
            cursor='hand2'
        )
        forgot_link.pack(side='right')
        forgot_link.bind("<Button-1>", self.show_password_reset)
        
        # Login button
        login_btn_frame = tk.Frame(form_frame, bg='#F8F9FA')
        login_btn_frame.pack(fill='x', pady=(0, 20))
        
        self.login_button = ProfessionalButton(
            login_btn_frame,
            text="Sign In",
            command=self.authenticate,
            style="primary",
            width=350,
            height=50
        )
        self.login_button.pack()
        
        # Loading indicator
        loading_frame = tk.Frame(form_frame, bg='#F8F9FA')
        loading_frame.pack(fill='x')
        
        self.loading_indicator = LoadingIndicator(loading_frame)
        
        # Status message
        self.status_label = tk.Label(
            form_frame,
            text="",
            font=("Segoe UI", 10),
            bg='#F8F9FA',
            fg='#DC3545'
        )
        self.status_label.pack(pady=(10, 0))
        
        # Bind Enter key
        self.root.bind('<Return>', lambda event: self.authenticate())
        self.username_entry.focus()
    
    def create_additional_options(self, parent):
        """Create additional login options"""
        options_frame = tk.Frame(parent, bg='#F8F9FA')
        options_frame.pack(fill='x', pady=(30, 0))
        
        # Divider
        divider_frame = tk.Frame(options_frame, bg='#F8F9FA')
        divider_frame.pack(fill='x', pady=(0, 20))
        
        divider_line1 = tk.Frame(divider_frame, height=1, bg='#DEE2E6')
        divider_line1.pack(side='left', fill='x', expand=True, pady=10)
        
        divider_text = tk.Label(
            divider_frame,
            text="  Additional Options  ",
            font=("Segoe UI", 9),
            bg='#F8F9FA',
            fg='#6C757D'
        )
        divider_text.pack(side='left')
        
        divider_line2 = tk.Frame(divider_frame, height=1, bg='#DEE2E6')
        divider_line2.pack(side='left', fill='x', expand=True, pady=10)
        
        # Quick access info
        info_frame = tk.Frame(options_frame, bg='#E3F2FD', relief='solid', bd=1)
        info_frame.pack(fill='x', pady=(0, 20))
        
        info_content = tk.Frame(info_frame, bg='#E3F2FD')
        info_content.pack(fill='x', padx=15, pady=12)
        
        info_icon = tk.Label(
            info_content,
            text="ℹ️",
            font=("Segoe UI", 12),
            bg='#E3F2FD'
        )
        info_icon.pack(side='left')
        
        info_text = tk.Label(
            info_content,
            text="Demo Access: admin / password123",
            font=("Segoe UI", 10, "bold"),
            bg='#E3F2FD',
            fg='#0066CC'
        )
        info_text.pack(side='left', padx=(10, 0))
        
        # Help and support
        help_frame = tk.Frame(options_frame, bg='#F8F9FA')
        help_frame.pack(fill='x')
        
        help_link = tk.Label(
            help_frame,
            text="Need Help? Contact Support",
            font=("Segoe UI", 10, "underline"),
            bg='#F8F9FA',
            fg='#6C757D',
            cursor='hand2'
        )
        help_link.pack()
        help_link.bind("<Button-1>", self.show_help)
    
    def create_status_bar(self):
        """Create bottom status bar"""
        status_bar = tk.Frame(self.root, bg='#212529', height=30)
        status_bar.pack(fill='x', side='bottom')
        status_bar.pack_propagate(False)
        
        status_content = tk.Frame(status_bar, bg='#212529')
        status_content.pack(fill='both', expand=True, padx=20, pady=5)
        
        # System status
        system_status = tk.Label(
            status_content,
            text="🟢 System Online • Secure Connection Active",
            font=("Segoe UI", 9),
            bg='#212529',
            fg='#28A745'
        )
        system_status.pack(side='left')
        
        # Current time
        self.time_label = tk.Label(
            status_content,
            text="",
            font=("Segoe UI", 9),
            bg='#212529',
            fg='#ADB5BD'
        )
        self.time_label.pack(side='right')
        
        self.update_time()
        
        # Version info
        version_label = tk.Label(
            status_content,
            text="v2.0 Enterprise",
            font=("Segoe UI", 9),
            bg='#212529',
            fg='#6C757D'
        )
        version_label.pack(side='right', padx=(0, 20))
    
    def toggle_password_visibility(self):
        """Toggle password field visibility"""
        if self.show_password_var.get():
            self.password_entry.config(show='')
        else:
            self.password_entry.config(show='●')
    
    def authenticate(self):
        """Handle authentication with professional feedback"""
        try:
            if self.loading:
                return
            
            username = self.username_entry.get().strip()
            password = self.password_entry.get().strip()
            
            # Validation
            if not username or not password:
                self.show_error("Please enter both username and password")
                return
            
            # Check if account is locked
            if self.is_account_locked():
                remaining_time = self.get_lockout_remaining_time()
                self.show_error(f"Account locked. Please wait {remaining_time} minutes.")
                return
            
            # Show loading state
            self.set_loading_state(True)
            self.status_label.config(text="Authenticating...", fg='#0066CC')
            
            # Simulate authentication delay for professional feel
            threading.Thread(target=self.perform_authentication, args=(username, password), daemon=True).start()
            
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            self.show_error("Authentication system error")
            self.set_loading_state(False)
    
    def perform_authentication(self, username, password):
        """Perform authentication in background thread"""
        try:
            # Add small delay for professional feel
            time.sleep(1.5)
            
            # Authenticate using security manager
            success, message, user_info = self.security_manager.authenticate_user(username, password)
            
            # Update UI on main thread
            self.root.after(0, self.handle_auth_result, success, message, user_info)
            
        except Exception as e:
            self.root.after(0, self.handle_auth_result, False, f"Authentication error: {str(e)}", {})
    
    def handle_auth_result(self, success, message, user_info):
        """Handle authentication result on main thread"""
        try:
            self.set_loading_state(False)
            
            if success:
                print("✅ Authentication successful!")
                self.authenticated = True
                
                # Reset failed attempts
                self.failed_attempts = 0
                
                # Show success message
                self.status_label.config(text="✅ Authentication successful!", fg='#28A745')
                
                # Professional success feedback
                self.show_success_dialog(user_info)
                
                # Start main application after short delay
                self.root.after(2000, self.start_main_application)
                
            else:
                print("❌ Authentication failed!")
                self.failed_attempts += 1
                
                # Check for account lockout
                if self.failed_attempts >= self.max_attempts:
                    self.lockout_time = time.time() + (5 * 60)  # 5 minutes
                    self.show_error(f"Account locked after {self.max_attempts} failed attempts. Please wait 5 minutes.")
                else:
                    remaining = self.max_attempts - self.failed_attempts
                    self.show_error(f"Invalid credentials. {remaining} attempts remaining.")
                
                # Clear password field
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
                
        except Exception as e:
            print(f"❌ Error handling authentication result: {e}")
            self.show_error("System error occurred")
    
    def show_success_dialog(self, user_info):
        """Show professional success dialog"""
        try:
            success_popup = tk.Toplevel(self.root)
            success_popup.title("Authentication Successful")
            success_popup.geometry("400x250")
            success_popup.configure(bg='white')
            success_popup.transient(self.root)
            success_popup.grab_set()
            
            # Center popup
            x = self.root.winfo_x() + (self.root.winfo_width() - 400) // 2
            y = self.root.winfo_y() + (self.root.winfo_height() - 250) // 2
            success_popup.geometry(f"400x250+{x}+{y}")
            
            # Success content
            content_frame = tk.Frame(success_popup, bg='white')
            content_frame.pack(fill='both', expand=True, padx=30, pady=30)
            
            # Success icon
            success_icon = tk.Label(
                content_frame,
                text="✅",
                font=("Segoe UI", 48),
                bg='white'
            )
            success_icon.pack(pady=(0, 20))
            
            # Success message
            success_title = tk.Label(
                content_frame,
                text="Welcome to AI Video Detection!",
                font=("Segoe UI", 16, "bold"),
                bg='white',
                fg='#212529'
            )
            success_title.pack(pady=(0, 10))
            
            # User info
            username = user_info.get('username', 'Unknown')
            role = user_info.get('role', 'user')
            
            user_info_text = f"User: {username}\nRole: {role.title()}\nAccess Level: Full System"
            
            user_info_label = tk.Label(
                content_frame,
                text=user_info_text,
                font=("Segoe UI", 11),
                bg='white',
                fg='#6C757D',
                justify='center'
            )
            user_info_label.pack(pady=(0, 20))
            
            # Auto-close after 3 seconds
            self.root.after(3000, success_popup.destroy)
            
        except Exception as e:
            print(f"Error showing success dialog: {e}")
    
    def set_loading_state(self, loading):
        """Set loading state with visual feedback"""
        self.loading = loading
        
        if loading:
            self.login_button.configure(state='disabled')
            self.loading_indicator.start()
            self.loading_indicator.pack(pady=(10, 0))
        else:
            self.login_button.configure(state='normal')
            self.loading_indicator.stop()
            self.loading_indicator.frame.pack_forget()
    
    def show_error(self, message):
        """Show error message with professional styling"""
        self.status_label.config(text=f"❌ {message}", fg='#DC3545')
        
        # Shake animation for visual feedback
        self.shake_window()
    
    def shake_window(self):
        """Subtle shake animation for error feedback"""
        try:
            original_x = self.root.winfo_x()
            original_y = self.root.winfo_y()
            
            for i in range(4):
                self.root.geometry(f"+{original_x + 5}+{original_y}")
                self.root.update()
                time.sleep(0.05)
                self.root.geometry(f"+{original_x - 5}+{original_y}")
                self.root.update()
                time.sleep(0.05)
            
            self.root.geometry(f"+{original_x}+{original_y}")
        except:
            pass
    
    def is_account_locked(self):
        """Check if account is currently locked"""
        return self.lockout_time > time.time()
    
    def get_lockout_remaining_time(self):
        """Get remaining lockout time in minutes"""
        if self.lockout_time > time.time():
            return int((self.lockout_time - time.time()) / 60) + 1
        return 0
    
    def show_password_reset(self, event):
        """Show password reset dialog"""
        messagebox.showinfo(
            "Password Reset",
            "🔒 Password Reset\n\n"
            "For security reasons, password reset requires:\n"
            "• Administrator approval\n"
            "• Identity verification\n"
            "• Security clearance\n\n"
            "Please contact your system administrator."
        )
    
    def show_help(self, event):
        """Show help dialog"""
        help_popup = tk.Toplevel(self.root)
        help_popup.title("Login Help & Support")
        help_popup.geometry("500x400")
        help_popup.configure(bg='white')
        help_popup.transient(self.root)
        
        # Center popup
        x = self.root.winfo_x() + (self.root.winfo_width() - 500) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - 400) // 2
        help_popup.geometry(f"500x400+{x}+{y}")
        
        help_content = tk.Frame(help_popup, bg='white')
        help_content.pack(fill='both', expand=True, padx=30, pady=30)
        
        title_label = tk.Label(
            help_content,
            text="🆘 Login Help & Support",
            font=("Segoe UI", 16, "bold"),
            bg='white',
            fg='#212529'
        )
        title_label.pack(pady=(0, 20))
        
        help_text = """
📋 LOGIN REQUIREMENTS:
• Valid username and password
• Active internet connection
• Supported browser or application

🔧 TROUBLESHOOTING:
• Check your credentials
• Ensure caps lock is off
• Clear browser cache
• Contact IT support if issues persist

🔒 SECURITY FEATURES:
• Account lockout after 3 failed attempts
• 5-minute lockout duration
• Secure password requirements
• Session monitoring

📞 SUPPORT CONTACT:
• IT Helpdesk: ext. 1234
• Email: <EMAIL>
• Emergency: Call security

🕒 SYSTEM STATUS:
• All systems operational
• Maintenance window: 2-4 AM daily
• Uptime: 99.9%
        """
        
        help_text_label = tk.Label(
            help_content,
            text=help_text.strip(),
            font=("Segoe UI", 10),
            bg='white',
            fg='#212529',
            justify='left',
            anchor='nw'
        )
        help_text_label.pack(fill='both', expand=True)
        
        close_btn = ProfessionalButton(
            help_content,
            text="Close",
            command=help_popup.destroy,
            style="secondary",
            width=100,
            height=35
        )
        close_btn.pack(pady=(20, 0))
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def start_main_application(self):
        """Start the main application after successful login"""
        try:
            print("🚀 Starting main application...")
            
            # Close login window
            self.root.destroy()
            
            # Try to import and start the main window
            try:
                from gui.main_window import MainWindow
                print("✅ Main window module imported successfully")
                
                # Create and run main window
                main_app = MainWindow()
                main_app.run()
                
            except ImportError as e:
                print(f"⚠️ Main window import error: {e}")
                print("🔄 Starting fallback interface...")
                self.start_fallback_interface()
                
        except Exception as e:
            print(f"❌ Error starting main application: {e}")
            messagebox.showerror(
                "Application Error", 
                f"Error starting main application:\n{str(e)}"
            )
    
    def start_fallback_interface(self):
        """Start fallback interface when main window fails"""
        try:
            # Create professional fallback window
            main_root = tk.Tk()
            main_root.title("AI Video Detection - Professional Dashboard")
            main_root.geometry("1200x800")
            main_root.configure(bg='#F8F9FA')
            
            # Main frame
            main_frame = tk.Frame(main_root, bg='#F8F9FA')
            main_frame.pack(fill='both', expand=True, padx=30, pady=30)
            
            # Header
            header_frame = tk.Frame(main_frame, bg='#0066CC', height=100)
            header_frame.pack(fill='x', pady=(0, 30))
            header_frame.pack_propagate(False)
            
            header_content = tk.Frame(header_frame, bg='#0066CC')
            header_content.pack(expand=True, padx=30, pady=20)
            
            title_label = tk.Label(
                header_content,
                text="🛡️ AI Video Detection - Professional Dashboard",
                font=("Segoe UI", 20, "bold"),
                bg='#0066CC',
                fg='white'
            )
            title_label.pack(expand=True)
            
            # Content area
            content_frame = tk.Frame(main_frame, bg='white', relief='solid', bd=2)
            content_frame.pack(fill='both', expand=True)
            
            content_inner = tk.Frame(content_frame, bg='white')
            content_inner.pack(fill='both', expand=True, padx=40, pady=40)
            
            # Content
            content_title = tk.Label(
                content_inner,
                text="🎯 System Dashboard",
                font=("Segoe UI", 18, "bold"),
                bg='white',
                fg='#212529'
            )
            content_title.pack(pady=(0, 20))
            
            # Status grid
            status_grid = tk.Frame(content_inner, bg='white')
            status_grid.pack(fill='x', pady=(0, 30))
            
            status_items = [
                ("🟢 System Status", "Online and Ready"),
                ("🟢 Authentication", "Successful"),
                ("🟢 Core Modules", "Loaded"),
                ("⚠️ Advanced Features", "Limited Mode")
            ]
            
            for i, (title, status) in enumerate(status_items):
                row = i // 2
                col = i % 2
                
                status_frame = tk.Frame(status_grid, bg='#F8F9FA', relief='solid', bd=1)
                status_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
                
                tk.Label(status_frame, text=title, font=("Segoe UI", 12, "bold"),
                        bg='#F8F9FA', fg='#212529').pack(pady=10)
                tk.Label(status_frame, text=status, font=("Segoe UI", 10),
                        bg='#F8F9FA', fg='#6C757D').pack(pady=(0, 10))
            
            status_grid.columnconfigure(0, weight=1)
            status_grid.columnconfigure(1, weight=1)
            
            # Features text
            features_text = """
📋 Available Functions:
• Camera access and testing
• Basic video processing
• System configuration
• Help and documentation

🔧 To access full features:
1. Install dependencies: pip install opencv-python numpy pillow
2. Download AI models: python download_models.py
3. Restart application
            """
            
            features_label = tk.Label(
                content_inner,
                text=features_text.strip(),
                font=("Segoe UI", 11),
                bg='white',
                fg='#212529',
                justify='left'
            )
            features_label.pack(pady=(0, 30))
            
            # Professional buttons
            button_frame = tk.Frame(content_inner, bg='white')
            button_frame.pack(fill='x')
            
            def test_camera():
                try:
                    import cv2
                    cap = cv2.VideoCapture(0)
                    if cap.isOpened():
                        ret, frame = cap.read()
                        if ret:
                            messagebox.showinfo(
                                "Camera Test", 
                                f"✅ Camera is working perfectly!\n\n"
                                f"Resolution: {frame.shape[1]}x{frame.shape[0]}\n"
                                f"Status: Ready for detection"
                            )
                        else:
                            messagebox.showwarning("Camera Test", "⚠️ Camera detected but cannot read frames")
                        cap.release()
                    else:
                        messagebox.showerror("Camera Test", "❌ No camera detected\n\nPlease check camera connection")
                except ImportError:
                    messagebox.showerror("Camera Test", "❌ OpenCV not available\n\nInstall with: pip install opencv-python")
                except Exception as e:
                    messagebox.showerror("Camera Test", f"❌ Camera error:\n{str(e)}")
            
            def open_settings():
                messagebox.showinfo(
                    "Settings", 
                    "⚙️ Professional Settings\n\n"
                    "This will include:\n"
                    "• Detection parameters\n"
                    "• Camera configuration\n"
                    "• Alert thresholds\n"
                    "• Export preferences"
                )
            
            def exit_app():
                if messagebox.askyesno("Exit", "Are you sure you want to exit?"):
                    main_root.destroy()
            
            # Create professional buttons
            ProfessionalButton(button_frame, text="📷 Test Camera", command=test_camera, 
                             style="primary", width=150, height=40).pack(side='left', padx=10)
            ProfessionalButton(button_frame, text="⚙️ Settings", command=open_settings,
                             style="secondary", width=120, height=40).pack(side='left', padx=10)
            ProfessionalButton(button_frame, text="🚪 Exit", command=exit_app,
                             style="danger", width=100, height=40).pack(side='right', padx=10)
            
            # Run the professional fallback interface
            main_root.mainloop()
            
        except Exception as e:
            print(f"❌ Professional fallback interface error: {e}")
            messagebox.showerror("Critical Error", "Unable to start any interface. Please check your Python installation.")
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            self.root.destroy()
    
    def run(self):
        """Run the professional login window"""
        print("🖥️ Launching professional login interface...")
        self.root.mainloop()


# Test the professional login interface
if __name__ == "__main__":
    print("🔧 Testing Professional Login Interface...")
    print("🎨 Features:")
    print("   • Professional split-panel design")
    print("   • Corporate branding and colors")
    print("   • Advanced security indicators")
    print("   • Loading animations and feedback")
    print("   • Professional button styling")
    print("   • Account lockout protection")
    print("   • Comprehensive error handling")
    
    app = ProfessionalLoginWindow()
    app.run()