"""
Check Database Schema
"""

import sqlite3
import os

def check_database_schema():
    """Check the current database schema"""
    db_path = "detection_results.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 DATABASE SCHEMA ANALYSIS")
        print("=" * 50)
        
        for table_name in tables:
            table = table_name[0]
            print(f"\n🔍 TABLE: {table}")
            print("-" * 30)
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, primary_key = col
                print(f"  {col_name}: {col_type}")
        
        # Check expression_detections specifically
        print(f"\n🎭 EXPRESSION_DETECTIONS TABLE DETAILS:")
        print("-" * 40)
        cursor.execute("PRAGMA table_info(expression_detections)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        print(f"Column names: {column_names}")
        
        if 'face_bbox' in column_names:
            print("⚠️ Found 'face_bbox' column - this should be 'coordinates'")
        
        if 'coordinates' in column_names:
            print("✅ Found 'coordinates' column")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database schema: {e}")

if __name__ == "__main__":
    check_database_schema()
