#!/usr/bin/env python3
"""
Camera Color Test Tool
Diagnoses and fixes grayscale camera issues
"""

import cv2
import numpy as np
import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
import time

class CameraColorTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Camera Color Diagnostic Tool")
        self.root.geometry("800x600")
        self.root.configure(bg='#2E86AB')
        
        self.video_cap = None
        self.is_running = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the diagnostic UI"""
        # Title
        title_label = tk.Label(self.root, text="🎥 Camera Color Diagnostic Tool", 
                              font=('Arial', 16, 'bold'), bg='#2E86AB', fg='white')
        title_label.pack(pady=10)
        
        # Info frame
        info_frame = tk.Frame(self.root, bg='white', relief='solid', bd=2)
        info_frame.pack(fill='x', padx=20, pady=10)
        
        self.info_text = tk.Text(info_frame, height=8, font=('Courier', 10))
        self.info_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Video display
        self.video_label = tk.Label(self.root, text="📷 Camera Preview\nPress 'Test Camera' to start", 
                                   font=('Arial', 12), bg='black', fg='white', 
                                   width=80, height=20)
        self.video_label.pack(pady=10)
        
        # Control buttons
        button_frame = tk.Frame(self.root, bg='#2E86AB')
        button_frame.pack(pady=10)
        
        self.test_btn = tk.Button(button_frame, text="🔍 Test Camera", 
                                 font=('Arial', 12, 'bold'), bg='#28a745', fg='white',
                                 command=self.test_camera, padx=20, pady=5)
        self.test_btn.pack(side='left', padx=5)
        
        self.stop_btn = tk.Button(button_frame, text="⏹️ Stop", 
                                 font=('Arial', 12, 'bold'), bg='#dc3545', fg='white',
                                 command=self.stop_camera, padx=20, pady=5)
        self.stop_btn.pack(side='left', padx=5)
        
        self.fix_btn = tk.Button(button_frame, text="🔧 Apply Color Fix", 
                                font=('Arial', 12, 'bold'), bg='#ffc107', fg='black',
                                command=self.apply_color_fix, padx=20, pady=5)
        self.fix_btn.pack(side='left', padx=5)
        
        # Status
        self.status_label = tk.Label(self.root, text="Ready to test camera", 
                                    font=('Arial', 10), bg='#2E86AB', fg='white')
        self.status_label.pack(pady=5)
        
    def log_info(self, message):
        """Log information to the text widget"""
        self.info_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.info_text.see(tk.END)
        self.root.update()
        
    def test_camera(self):
        """Test camera and diagnose color issues"""
        try:
            self.log_info("🔍 Starting camera diagnostic...")
            
            # Try to open camera
            self.video_cap = cv2.VideoCapture(0)
            if not self.video_cap.isOpened():
                self.log_info("❌ Cannot open camera")
                messagebox.showerror("Error", "Cannot open camera")
                return
                
            self.log_info("✅ Camera opened successfully")
            
            # Test frame capture
            ret, frame = self.video_cap.read()
            if not ret:
                self.log_info("❌ Cannot read frame from camera")
                return
                
            self.log_info(f"✅ Frame captured: {frame.shape}")
            
            # Analyze frame
            self.analyze_frame(frame)
            
            # Start live preview
            self.is_running = True
            self.update_preview()
            
        except Exception as e:
            self.log_info(f"❌ Error: {e}")
            
    def analyze_frame(self, frame):
        """Analyze frame for color information"""
        try:
            height, width = frame.shape[:2]
            self.log_info(f"📊 Frame dimensions: {width}x{height}")
            
            if len(frame.shape) == 3:
                channels = frame.shape[2]
                self.log_info(f"📊 Color channels: {channels}")
                
                if channels == 3:
                    # Analyze color channels
                    b_channel = frame[:, :, 0]
                    g_channel = frame[:, :, 1] 
                    r_channel = frame[:, :, 2]
                    
                    # Calculate statistics
                    b_mean = b_channel.mean()
                    g_mean = g_channel.mean()
                    r_mean = r_channel.mean()
                    
                    self.log_info(f"📊 Channel means: B={b_mean:.1f}, G={g_mean:.1f}, R={r_mean:.1f}")
                    
                    # Calculate channel differences
                    bg_diff = np.abs(b_channel.astype(float) - g_channel.astype(float)).mean()
                    gr_diff = np.abs(g_channel.astype(float) - r_channel.astype(float)).mean()
                    br_diff = np.abs(b_channel.astype(float) - r_channel.astype(float)).mean()
                    
                    total_diff = bg_diff + gr_diff + br_diff
                    self.log_info(f"📊 Channel differences: B-G={bg_diff:.2f}, G-R={gr_diff:.2f}, B-R={br_diff:.2f}")
                    self.log_info(f"📊 Total color difference: {total_diff:.2f}")
                    
                    # Diagnosis
                    if total_diff < 5.0:
                        self.log_info("⚠️ DIAGNOSIS: Camera is in GRAYSCALE mode")
                        self.log_info("🔧 SOLUTION: Apply color enhancement or check camera settings")
                        self.status_label.config(text="⚠️ Grayscale detected - needs color fix", fg='orange')
                    else:
                        self.log_info("✅ DIAGNOSIS: Camera is in TRUE COLOR mode")
                        self.status_label.config(text="✅ True color detected", fg='lightgreen')
                        
            else:
                self.log_info("⚠️ Frame is grayscale (2D array)")
                self.status_label.config(text="⚠️ Grayscale frame detected", fg='orange')
                
        except Exception as e:
            self.log_info(f"❌ Analysis error: {e}")
            
    def update_preview(self):
        """Update camera preview"""
        if not self.is_running or not self.video_cap:
            return
            
        try:
            ret, frame = self.video_cap.read()
            if ret:
                # Resize for display
                display_frame = cv2.resize(frame, (640, 480))
                
                # Convert for display
                frame_rgb = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
                photo = ImageTk.PhotoImage(image=pil_image)
                
                self.video_label.config(image=photo, text="")
                self.video_label.image = photo
                
            # Schedule next update
            self.root.after(33, self.update_preview)  # ~30 FPS
            
        except Exception as e:
            self.log_info(f"❌ Preview error: {e}")
            
    def apply_color_fix(self):
        """Apply color enhancement fix"""
        if not self.video_cap:
            messagebox.showwarning("Warning", "Start camera test first")
            return
            
        try:
            self.log_info("🔧 Applying color enhancement settings...")
            
            # Try to set color properties
            try:
                self.video_cap.set(cv2.CAP_PROP_SATURATION, 150)
                self.log_info("✅ Increased saturation to 150")
            except:
                self.log_info("⚠️ Cannot control saturation")
                
            try:
                self.video_cap.set(cv2.CAP_PROP_CONTRAST, 140)
                self.log_info("✅ Increased contrast to 140")
            except:
                self.log_info("⚠️ Cannot control contrast")
                
            try:
                self.video_cap.set(cv2.CAP_PROP_BRIGHTNESS, 120)
                self.log_info("✅ Adjusted brightness to 120")
            except:
                self.log_info("⚠️ Cannot control brightness")
                
            # Test the fix
            ret, frame = self.video_cap.read()
            if ret:
                self.analyze_frame(frame)
                
            self.log_info("🔧 Color fix applied - check the results above")
            
        except Exception as e:
            self.log_info(f"❌ Fix error: {e}")
            
    def stop_camera(self):
        """Stop camera"""
        self.is_running = False
        if self.video_cap:
            self.video_cap.release()
            self.video_cap = None
        self.video_label.config(image="", text="📷 Camera Stopped\nPress 'Test Camera' to restart")
        self.status_label.config(text="Camera stopped", fg='white')
        self.log_info("⏹️ Camera stopped")
        
    def run(self):
        """Run the diagnostic tool"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """Handle window closing"""
        self.stop_camera()
        self.root.destroy()

if __name__ == "__main__":
    print("🎥 Starting Camera Color Diagnostic Tool...")
    app = CameraColorTest()
    app.run()
